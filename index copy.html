<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>西安易复诊铭智网络科技有限公司</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="shortcut icon" href="images/favicon.ico" type="image/x-icon">
</head>

<body>
    <!-- 导航栏 -->
    <header>
        <div class="container">
            <div class="logo">
                <img class="img" src="images/logo.png" alt="logo">
                <div style="position: relative;">
                    <h1>西安易复诊铭智网络科技有限公司</h1>
                    <span>Xi'an YiFuZhen MingZhi Network Technology Co., Ltd.</span>
                </div>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html" class="active">首页</a></li>
                    <li><a href="about.html">关于我们</a></li>
                    <li><a href="product.html">产品服务</a></li>
                    <li><a href="#team">团队介绍</a></li>
                    <li><a href="#contact">联系我们</a></li>
                </ul>
            </nav>
            <div class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <!-- 首页横幅 -->
    <section id="home" class="hero" style="background-image: url('images/hero-bg.jpg');">
        <div class="container">
            <div class="hero-content">
                <h1 class="animate fade-in-down">智慧监管 · 服务便民</h1>
                <p class="animate fade-in-up delay-1">医保电子处方中心的流转应用及监管系统</p>
                <div class="hero-btns animate fade-in-up delay-2">
                    <a href="product.html" class="btn btn-primary">了解产品</a>
                    <a href="#contact" class="btn btn-outline">联系我们</a>
                </div>
            </div>
        </div>
    </section>

    <!-- 关于我们 -->
    <section id="about" class="about">
        <div class="container">
            <div class="section-header scroll-animation scroll-fade-up">
                <h2>关于我们</h2>
                <p>专注医药处方领域十余年，为医疗机构提供全方位解决方案</p>
            </div>
            <div class="about-content">
                <div class="about-img scroll-animation scroll-fade-right">
                    <img src="images/about-us.jpg" alt="关于我们">
                </div>
                <div class="about-text scroll-animation scroll-fade-left">
                    <h3>医药处方科技有限公司</h3>
                    <p>我们是一家专注于医药处方管理系统研发的高新技术企业，致力于通过科技手段提升医疗处方的安全性、规范性和便捷性。</p>
                    <p>公司拥有一支由医药专家、软件工程师和数据科学家组成的专业团队，为医院、药店和医疗机构提供定制化的处方管理解决方案。</p>
                    <div class="about-features">
                        <div class="feature scroll-animation scroll-fade-up">
                            <i class="fas fa-shield-alt pulse"></i>
                            <h4>安全可靠</h4>
                            <p>严格的数据加密和权限管理</p>
                        </div>
                        <div class="feature scroll-animation scroll-fade-up delay-1">
                            <i class="fas fa-bolt pulse"></i>
                            <h4>高效便捷</h4>
                            <p>智能化处方审核与管理</p>
                        </div>
                        <div class="feature scroll-animation scroll-fade-up delay-2">
                            <i class="fas fa-chart-line pulse"></i>
                            <h4>数据分析</h4>
                            <p>深度挖掘处方数据价值</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 产品服务 - 全屏轮播图 -->
    <section id="products" class="products-carousel">
        <!-- 标题区域 -->
        <div class="carousel-header">
            <div class="container">
                <div class="section-header light scroll-animation scroll-fade-up">
                    <h2>产品服务</h2>
                    <p>全面的医药处方解决方案，满足不同场景需求</p>
                </div>
            </div>
        </div>

        <!-- 全屏轮播图 -->
        <div class="fullscreen-product-carousel">
            <div class="carousel-slides">
                <!-- 幻灯片1 -->
                <div class="carousel-slide active" style="background-image: url('images/product/product-1.jpg');">
                    <div class="slide-overlay"></div>
                    <div class="slide-content">
                        <div>左归丸（仲景） 45g</div>
                        <div>滋阴补肾类</div>
                        <div>
                            <span>市场价： </span>
                            <span>79.00</span>
                        </div>
                        <div>
                            <span>价格： </span>
                            <span>79.00</span>
                        </div>
                        <div>滋肾补阴</div>
                        <a href="product.html#product-1" class="btn btn-primary slide-btn">了解更多</a>
                    </div>
                </div>

                <!-- 幻灯片2 -->
                <div class="carousel-slide" style="background-image: url('images/product-2.jpg');">
                    <div class="slide-overlay"></div>
                    <div class="slide-content">
                        <div class="slide-text">
                            <h3 class="slide-title">药品管理平台</h3>
                            <p class="slide-description">一体化药品库存、采购与效期管理，实现药品全生命周期的精细化管理</p>
                            <a href="product.html#product-2" class="btn btn-primary slide-btn">了解更多</a>
                        </div>
                    </div>
                </div>

                <!-- 幻灯片3 -->
                <div class="carousel-slide" style="background-image: url('images/product-3.jpg');">
                    <div class="slide-overlay"></div>
                    <div class="slide-content">
                        <div class="slide-text">
                            <h3 class="slide-title">医药数据分析</h3>
                            <p class="slide-description">处方数据挖掘与分析服务，为医疗机构提供用药决策支持与优化建议</p>
                            <a href="product.html#product-3" class="btn btn-primary slide-btn">了解更多</a>
                        </div>
                    </div>
                </div>

                <!-- 幻灯片4 -->
                <div class="carousel-slide" style="background-image: url('images/生成处方公司官网背景图(4).png');">
                    <div class="slide-overlay"></div>
                    <div class="slide-content">
                        <div class="slide-text">
                            <h3 class="slide-title">处方流转平台</h3>
                            <p class="slide-description">连接医院、药店和患者的处方流转服务平台，实现处方信息安全高效流转</p>
                            <a href="product.html#product-4" class="btn btn-primary slide-btn">了解更多</a>
                        </div>
                    </div>
                </div>

                <!-- 幻灯片5 -->
                <div class="carousel-slide" style="background-image: url('images/生成处方公司官网背景图(8).png');">
                    <div class="slide-overlay"></div>
                    <div class="slide-content">
                        <div class="slide-text">
                            <h3 class="slide-title">医保监管系统</h3>
                            <p class="slide-description">智能化医保基金监管系统，提供实时监控、风险预警和智能审核功能</p>
                            <a href="product.html#product-5" class="btn btn-primary slide-btn">了解更多</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 导航按钮 -->
            <button class="carousel-nav-btn prev-btn" onclick="changeSlide(-1)">
                <i class="fas fa-chevron-left"></i>
            </button>
            <button class="carousel-nav-btn next-btn" onclick="changeSlide(1)">
                <i class="fas fa-chevron-right"></i>
            </button>

            <!-- 指示器 -->
            <div class="carousel-dots">
                <span class="dot active" onclick="goToSlide(0)"></span>
                <span class="dot" onclick="goToSlide(1)"></span>
                <span class="dot" onclick="goToSlide(2)"></span>
                <span class="dot" onclick="goToSlide(3)"></span>
                <span class="dot" onclick="goToSlide(4)"></span>
            </div>
        </div>
    </section>

    <!-- 团队介绍 -->
    <section id="team" class="team">
        <div class="container">
            <div class="section-header scroll-animation scroll-fade-up">
                <h2>专业团队</h2>
                <p>由医药专家、技术精英组成的核心团队</p>
            </div>
            <div class="team-members">
                <div class="member scroll-animation scroll-fade-up">
                    <div class="member-img">
                        <img src="images/team-1.jpg" alt="团队成员">
                    </div>
                    <div class="member-info">
                        <h3>张医生</h3>
                        <p>首席医疗顾问</p>
                        <div class="social-links">
                            <a href="#"><i class="fab fa-linkedin"></i></a>
                            <a href="#"><i class="fab fa-twitter"></i></a>
                        </div>
                    </div>
                </div>
                <div class="member scroll-animation scroll-fade-up delay-1">
                    <div class="member-img">
                        <img src="images/team-2.jpg" alt="团队成员">
                    </div>
                    <div class="member-info">
                        <h3>李工程师</h3>
                        <p>技术总监</p>
                        <div class="social-links">
                            <a href="#"><i class="fab fa-linkedin"></i></a>
                            <a href="#"><i class="fab fa-github"></i></a>
                        </div>
                    </div>
                </div>
                <div class="member scroll-animation scroll-fade-up delay-2">
                    <div class="member-img">
                        <img src="images/team-3.jpg" alt="团队成员">
                    </div>
                    <div class="member-info">
                        <h3>王博士</h3>
                        <p>数据科学家</p>
                        <div class="social-links">
                            <a href="#"><i class="fab fa-linkedin"></i></a>
                            <a href="#"><i class="fab fa-researchgate"></i></a>
                        </div>
                    </div>
                </div>
                <div class="member scroll-animation scroll-fade-up delay-3">
                    <div class="member-img">
                        <img src="images/team-4.jpg" alt="团队成员">
                    </div>
                    <div class="member-info">
                        <h3>赵经理</h3>
                        <p>市场总监</p>
                        <div class="social-links">
                            <a href="#"><i class="fab fa-linkedin"></i></a>
                            <a href="#"><i class="fab fa-weixin"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 联系我们 -->
    <section id="contact" class="contact" style="background-image: url('images/生成处方公司官网背景图(8).png');">
        <div class="container">
            <div class="section-header light scroll-animation scroll-fade-up">
                <h2>联系我们</h2>
                <p>期待与您沟通合作，共创医药行业美好未来</p>
            </div>
            <div class="contact-content">
                <div class="contact-info scroll-animation scroll-fade-right">
                    <div class="info-item">
                        <i class="fas fa-map-marker-alt pulse"></i>
                        <div>
                            <h3>公司地址</h3>
                            <p>北京市海淀区中关村科技园区</p>
                        </div>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-phone pulse"></i>
                        <div>
                            <h3>联系电话</h3>
                            <p>010-12345678</p>
                        </div>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-envelope pulse"></i>
                        <div>
                            <h3>电子邮箱</h3>
                            <p><EMAIL></p>
                        </div>
                    </div>
                </div>
                <div class="contact-form scroll-animation scroll-fade-left">
                    <form>
                        <div class="form-group">
                            <input type="text" placeholder="您的姓名" required>
                        </div>
                        <div class="form-group">
                            <input type="email" placeholder="您的邮箱" required>
                        </div>
                        <div class="form-group">
                            <input type="text" placeholder="主题">
                        </div>
                        <div class="form-group">
                            <textarea placeholder="您的留言" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">发送信息</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>医药处方科技</h2>
                    <p>专业医药解决方案提供商</p>
                </div>
                <div class="footer-links">
                    <h3>快速链接</h3>
                    <ul>
                        <li><a href="index.html">首页</a></li>
                        <li><a href="about.html">关于我们</a></li>
                        <li><a href="product.html">产品服务</a></li>
                        <li><a href="#team">团队介绍</a></li>
                        <li><a href="#contact">联系我们</a></li>
                    </ul>
                </div>
                <div class="footer-newsletter">
                    <h3>订阅我们</h3>
                    <p>获取最新的医药行业资讯和产品更新</p>
                    <form>
                        <input type="email" placeholder="您的邮箱地址">
                        <button type="submit"><i class="fas fa-paper-plane"></i></button>
                    </form>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 医药处方科技有限公司. 保留所有权利.</p>
                <div class="social-links">
                    <a href="#"><i class="fab fa-weixin"></i></a>
                    <a href="#"><i class="fab fa-weibo"></i></a>
                    <a href="#"><i class="fab fa-linkedin"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>

    <script>
        // 全屏产品轮播图功能
        let currentSlideIndex = 0;
        const slides = document.querySelectorAll('.carousel-slide');
        const dots = document.querySelectorAll('.dot');
        const totalSlides = slides.length;
        let autoSlideInterval;

        // 显示指定的幻灯片
        function showSlide(index) {
            // 移除所有活动状态
            slides.forEach(slide => {
                slide.classList.remove('active');
                slide.style.opacity = '0';
                slide.style.transform = 'scale(1.1)';
            });
            dots.forEach(dot => dot.classList.remove('active'));

            // 添加活动状态到当前幻灯片
            setTimeout(() => {
                slides[index].classList.add('active');
                slides[index].style.opacity = '1';
                slides[index].style.transform = 'scale(1)';
                dots[index].classList.add('active');
            }, 100);
        }

        // 切换幻灯片
        function changeSlide(direction) {
            currentSlideIndex += direction;

            if (currentSlideIndex >= totalSlides) {
                currentSlideIndex = 0;
            } else if (currentSlideIndex < 0) {
                currentSlideIndex = totalSlides - 1;
            }

            showSlide(currentSlideIndex);
            resetAutoSlide();
        }

        // 跳转到指定幻灯片
        function goToSlide(index) {
            currentSlideIndex = index;
            showSlide(currentSlideIndex);
            resetAutoSlide();
        }

        // 自动轮播
        function autoSlide() {
            currentSlideIndex = (currentSlideIndex + 1) % totalSlides;
            showSlide(currentSlideIndex);
        }

        // 开始自动轮播
        function startAutoSlide() {
            autoSlideInterval = setInterval(autoSlide, 5000); // 每5秒切换一次
        }

        // 停止自动轮播
        function stopAutoSlide() {
            clearInterval(autoSlideInterval);
        }

        // 重置自动轮播
        function resetAutoSlide() {
            stopAutoSlide();
            startAutoSlide();
        }

        // 页面加载完成后初始化轮播图
        document.addEventListener('DOMContentLoaded', function () {
            // 初始化第一张幻灯片
            if (slides.length > 0) {
                showSlide(0);

                // 开始自动轮播
                // startAutoSlide();

                // 鼠标悬停时暂停自动轮播
                const carousel = document.querySelector('.fullscreen-product-carousel');
                if (carousel) {
                    carousel.addEventListener('mouseenter', stopAutoSlide);
                    carousel.addEventListener('mouseleave', startAutoSlide);
                }

                // 键盘导航支持
                document.addEventListener('keydown', function (event) {
                    if (event.key === 'ArrowLeft') {
                        changeSlide(-1);
                    } else if (event.key === 'ArrowRight') {
                        changeSlide(1);
                    }
                });

                // 触摸滑动支持（移动端）
                let startX = 0;
                let endX = 0;

                carousel.addEventListener('touchstart', function (event) {
                    startX = event.touches[0].clientX;
                });

                carousel.addEventListener('touchend', function (event) {
                    endX = event.changedTouches[0].clientX;
                    handleSwipe();
                });

                function handleSwipe() {
                    const swipeThreshold = 50;
                    const diff = startX - endX;

                    if (Math.abs(diff) > swipeThreshold) {
                        if (diff > 0) {
                            changeSlide(1); // 向左滑动，显示下一张
                        } else {
                            changeSlide(-1); // 向右滑动，显示上一张
                        }
                    }
                }
            }
        });
    </script>
</body>

</html>