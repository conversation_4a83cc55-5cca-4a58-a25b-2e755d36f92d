/* Product页面专用功能 */

// 全屏产品轮播图功能
let currentSlideIndex = 0;
const slides = document.querySelectorAll('.carousel-slide');
const dots = document.querySelectorAll('.dot');
const totalSlides = slides.length;
let autoSlideInterval;

// 显示指定的幻灯片
function showSlide(index) {
    // 移除所有活动状态
    slides.forEach(slide => {
        slide.classList.remove('active');
        slide.style.opacity = '0';
        slide.style.transform = 'scale(1.1)';
    });
    dots.forEach(dot => dot.classList.remove('active'));

    // 添加活动状态到当前幻灯片
    setTimeout(() => {
        slides[index].classList.add('active');
        slides[index].style.opacity = '1';
        slides[index].style.transform = 'scale(1)';
        dots[index].classList.add('active');
    }, 100);
}

// 切换幻灯片
function changeSlide(direction) {
    currentSlideIndex += direction;

    if (currentSlideIndex >= totalSlides) {
        currentSlideIndex = 0;
    } else if (currentSlideIndex < 0) {
        currentSlideIndex = totalSlides - 1;
    }

    showSlide(currentSlideIndex);
    resetAutoSlide();
}

// 跳转到指定幻灯片
function goToSlide(index) {
    currentSlideIndex = index;
    showSlide(currentSlideIndex);
    resetAutoSlide();
}

// 自动轮播
function autoSlide() {
    currentSlideIndex = (currentSlideIndex + 1) % totalSlides;
    showSlide(currentSlideIndex);
}

// 开始自动轮播
function startAutoSlide() {
    autoSlideInterval = setInterval(autoSlide, 5000); // 每5秒切换一次
}

// 停止自动轮播
function stopAutoSlide() {
    clearInterval(autoSlideInterval);
}

// 重置自动轮播
function resetAutoSlide() {
    stopAutoSlide();
    startAutoSlide();
}

// 页面加载完成后初始化轮播图
document.addEventListener('DOMContentLoaded', function () {
    // 初始化第一张幻灯片
    if (slides.length > 0) {
        showSlide(0);

        // 开始自动轮播
        startAutoSlide();

        // 鼠标悬停时暂停自动轮播
        const carousel = document.querySelector('.fullscreen-product-carousel');
        if (carousel) {
            carousel.addEventListener('mouseenter', stopAutoSlide);
            carousel.addEventListener('mouseleave', startAutoSlide);
        }

        // 键盘导航支持
        document.addEventListener('keydown', function (event) {
            if (event.key === 'ArrowLeft') {
                changeSlide(-1);
            } else if (event.key === 'ArrowRight') {
                changeSlide(1);
            }
        });

        // 触摸滑动支持（移动端）
        let startX = 0;
        let endX = 0;

        carousel.addEventListener('touchstart', function (event) {
            startX = event.touches[0].clientX;
        });

        carousel.addEventListener('touchend', function (event) {
            endX = event.changedTouches[0].clientX;
            handleSwipe();
        });

        function handleSwipe() {
            const swipeThreshold = 50;
            const diff = startX - endX;

            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    changeSlide(1); // 向左滑动，显示下一张
                } else {
                    changeSlide(-1); // 向右滑动，显示上一张
                }
            }
        }
    }
});
