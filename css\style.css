/* 全局样式 */
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --danger-color: #e74c3c;
    --success-color: #27ae60;
    --text-color: #333;
    --light-text: #fff;
    --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f8f9fa;
}

a {
    text-decoration: none;
    color: var(--dark-color);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-header h2 {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: var(--dark-color);
    position: relative;
    display: inline-block;
}

.section-header h2::after {
    content: '';
    position: absolute;
    width: 60px;
    height: 3px;
    background-color: var(--primary-color);
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.section-header p {
    font-size: 1.1rem;
    color: #666;
}

.section-header.light h2,
.section-header.light p {
    color: var(--light-text);
}

.section-header.light h2::after {
    background-color: var(--light-text);
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    border-radius: 30px;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--light-text);
    border: 2px solid var(--primary-color);
}

.btn-primary:hover {
    background-color: transparent;
    color: var(--primary-color);
}

.btn-outline {
    background-color: transparent;
    color: var(--light-text);
    border: 2px solid var(--light-text);
}

.btn-outline:hover {
    background-color: var(--light-text);
    color: var(--dark-color);
}

.btn-sm {
    padding: 8px 20px;
    font-size: 0.9rem;
}

/* 导航栏样式 */
header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: var(--box-shadow);
    z-index: 1000;
    padding: 15px 0;
    transition: var(--transition);
}

header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
}

.logo .img {
    width: 60px;
    height: 60px;
    margin-right: 15px;
}

.logo div {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.logo h1 {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin: 0;
    font-weight: 700;
    letter-spacing: 0.5px;
}

.logo span {
    font-size: 0.85rem;
    color: #000000;
    font-weight: 400;
    margin-top: 2px;
    letter-spacing: 2.3px;
    opacity: 0.8;
    transition: var(--transition);
}

.logo:hover span {
    opacity: 1;
    color: var(--primary-color);
}

nav ul {
    display: flex;
}

nav ul li {
    margin-left: 30px;
}

nav ul li a {
    font-weight: 600;
    position: relative;
    padding-bottom: 5px;
}

nav ul li a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    bottom: 0;
    left: 0;
    transition: var(--transition);
}

nav ul li a:hover::after,
nav ul li a.active::after {
    width: 100%;
}

.mobile-menu-btn {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* 首页横幅 */
.hero {
    height: 100vh;
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    display: flex;
    align-items: center;
    position: relative;
    margin-top: 0;
    padding-top: 80px;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
}

.hero .container {
    position: relative;
    z-index: 1;
}

.hero-content {
    max-width: 700px;
    color: var(--light-text);
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 20px;
    animation: fadeInDown 1s ease;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    animation: fadeInUp 1s ease 0.3s;
    animation-fill-mode: both;
}

.hero-btns {
    display: flex;
    gap: 15px;
    animation: fadeInUp 1s ease 0.6s;
    animation-fill-mode: both;
}

/* 关于我们 */
.about-content {
    display: flex;
    gap: 50px;
    align-items: center;
}

.about-images {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding-right: 25px;
}

.about-img-main {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 300px;
}

.about-img-main:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.about-img-secondary {
    margin-top: 80px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    height: 300px;
}

.about-img-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
}



.about-img-main img,
.about-img-secondary img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    display: block;
    transition: transform 0.3s ease;
}

.about-img-main:hover img,
.about-img-secondary:hover img {
    transform: scale(1.02);
}

/* 保持原有样式兼容性 */
.about-img {
    flex: 1;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.about-text {
    flex: 1;
    padding-left: 25px;
}

.about-text h3 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: var(--dark-color);
}

.about-text p {
    margin-bottom: 20px;
    color: #666;
}

.about-features {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    margin-top: 30px;
}

.feature {
    flex: 1;
    min-width: 200px;
    text-align: center;
    padding: 20px;
    border-radius: 5px;
    background-color: #fff;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.feature:hover {
    transform: translateY(-10px);
}

.feature i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.feature h4 {
    font-size: 1.2rem;
    margin-bottom: 10px;
}

/* 产品服务 */
.products {
    background-size: cover;
    background-position: center;
    position: relative;
    color: var(--light-text);
}

.products::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
}

.products .container {
    position: relative;
    z-index: 1;
}

/* 产品详情页面样式 */
.product-section {
    padding: 60px 0;
}

.product-detail {
    display: flex;
    align-items: center;
    gap: 60px;
    margin-bottom: 60px;
    min-height: 420px;
}

.product-detail-img {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.product-detail-img img {
    max-width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    /* 去除白色背景的方法 */
    mix-blend-mode: multiply;
    background: transparent;
    /* 或者使用滤镜方法 */
    filter: contrast(1.1) brightness(1.1);
}

.product-detail-img img:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* 产品详情图片容器基础样式 */
.product-detail-img {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
    position: relative;
}

/* 去除白底的特殊处理 */
.product-detail-img.remove-white-bg {
    overflow: hidden;
    /* 使用更深的背景来更好地融合白底图片 */
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.product-detail-img.remove-white-bg img {
    filter:
        brightness(0.95)
        contrast(1.15)
        saturate(1.1)
        drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
    mix-blend-mode: multiply;
    background: transparent;
    border-radius: 15px;
    /* 确保图片完全透明背景 */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
}

/* 悬停效果调整 */
.product-detail-img.remove-white-bg img:hover {
    transform: translateY(-5px) scale(1.02);
    filter:
        brightness(0.98)
        contrast(1.1)
        saturate(1.15)
        drop-shadow(0 15px 30px rgba(0, 0, 0, 0.15));
}

.product-detail-info {
    flex: 1;
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.product-detail-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 18px;
    line-height: 1.3;
}

.product-detail-info > p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.8;
    margin-bottom: 30px;
}

.product-features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
}

.feature-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.feature-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-top: 2px;
    flex-shrink: 0;
}

.feature-text h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.feature-text p {
    font-size: 0.95rem;
    color: #666;
    line-height: 1.5;
    margin: 0;
}

/* 产品服务 - 轮播图样式（紧凑版） */
.products-carousel {
    position: relative;
    width: 100%;
    background-color: #f8f9fa;
}

.carousel-header {
    padding: 50px 0 25px;
    background-color: #f8f9fa;
    position: relative;
}

.carousel-header .container {
    position: relative;
    z-index: 1;
}

.carousel-header .section-header h2,
.carousel-header .section-header p {
    color: var(--dark-color);
}

.carousel-header .section-header h2::after {
    background-color: var(--primary-color);
}

/* 轮播图容器 - 大幅缩小以协调整体布局 */
.fullscreen-product-carousel {
    position: relative;
    width: 100%;
    min-height: 360px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    overflow: hidden;
}

.carousel-slides {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 360px;
}

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 15px;
}

.carousel-slide.active {
    opacity: 1;
    transform: translateY(0);
}

/* 幻灯片内容 */
.slide-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding: 20px;
}

.product-card-content {
    background: white;
    border-radius: 12px;
    padding: 0;
    max-width: 750px;
    width: 100%;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
    animation: slideInUp 0.8s ease-out;
    border: 1px solid #e9ecef;
    overflow: hidden;
    display: flex;
    align-items: stretch;
    min-height: 260px;
}

.product-image {
    flex: 1;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image img {
    width: 180px;
    height: 180px;
    transition: transform 0.3s ease;
}

.product-card-content:hover .product-image img {
    transform: scale(1.05);
}

.product-info {
    flex: 1;
    padding: 20px 25px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: left;
}

.product-badge {
    display: inline-block;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 8px;
    line-height: 1.3;
}

.product-description {
    font-size: 0.9rem;
    color: #666;
    line-height: 1.5;
    margin-bottom: 15px;
}

.product-pricing {
    margin-bottom: 25px;
}

.price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.price-label {
    font-size: 1rem;
    color: #666;
    font-weight: 500;
}

.price-value {
    font-size: 1.2rem;
    font-weight: 700;
}

.market-price {
    color: #999;
    text-decoration: line-through;
}

.current-price {
    color: var(--primary-color);
    font-size: 1.4rem;
}

.product-features {
    display: flex;
    justify-content: flex-start;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(52, 152, 219, 0.1);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    color: var(--primary-color);
    font-weight: 500;
}

.feature-item i {
    font-size: 0.9rem;
}

.product-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-start;
    flex-wrap: wrap;
    margin-top: auto;
}

.slide-btn {
    padding: 8px 16px;
    font-size: 0.85rem;
    font-weight: 600;
    border-radius: 18px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    min-width: 100px;
    justify-content: center;
}

.slide-btn.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: 0 3px 12px rgba(52, 152, 219, 0.3);
}

.slide-btn.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.slide-btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.slide-btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* 导航按钮 - 缩小尺寸 */
.carousel-nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    width: 45px;
    height: 45px;
    border-radius: 50%;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    backdrop-filter: blur(10px);
}

.carousel-nav-btn:hover {
    background: rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.6);
    transform: translateY(-50%) scale(1.1);
}

.prev-btn {
    left: 20px;
}

.next-btn {
    right: 20px;
}

/* 指示器 */
.carousel-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
    z-index: 10;
    background: rgba(0, 0, 0, 0.3);
    padding: 10px 15px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
    max-width: 90%;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.carousel-dots::-webkit-scrollbar {
    display: none;
}

.dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: rgba(8, 8, 8, 0.4);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.6);
    flex-shrink: 0;
    position: relative;
}

.dot.active {
    background: var(--primary-color);
    transform: scale(1.3);
    box-shadow: 0 0 8px rgba(52, 152, 219, 0.8);
    border-color: white;
}

.dot:hover {
    background: rgba(255, 255, 255, 0.7);
    transform: scale(1.1);
}

/* 指示器提示信息 */
.dot::before {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    margin-bottom: 8px;
    pointer-events: none;
    z-index: 1000;
}

.dot::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    margin-bottom: 3px;
    pointer-events: none;
    z-index: 1000;
}

.dot:hover::before,
.dot:hover::after {
    opacity: 1;
    visibility: visible;
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 团队介绍 */
.team-members {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    justify-content: center;
}

.member {
    flex: 1;
    min-width: 250px;
    max-width: 280px;
    background-color: #fff;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.member:hover {
    transform: translateY(-10px);
}

.member-img {
    height: 280px;
    overflow: hidden;
}

.member-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.member:hover .member-img img {
    transform: scale(1.1);
}

.member-info {
    padding: 20px;
    text-align: center;
}

.member-info h3 {
    font-size: 1.3rem;
    margin-bottom: 5px;
    color: var(--dark-color);
}

.member-info p {
    color: #666;
    margin-bottom: 15px;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: var(--light-color);
    color: var(--dark-color);
    transition: var(--transition);
}

.social-links a:hover {
    background-color: var(--primary-color);
    color: var(--light-text);
}

.textIndent {
    text-indent: 15px;
}


/* 权威合作 */
.partners {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    position: relative;
}

.partners-content {
    margin-top: 60px;
}

.partners-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 80px;
}

.partner-item {
    background: white;
    border-radius: 15px;
    padding: 30px 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 120px;
}

.partner-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
}

.partner-logo {
    width: 100%;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.partner-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: all 0.3s ease;
}

.partner-item:hover .partner-logo img {
    transform: scale(1.05);
}

/* 合作成果统计 */
.cooperation-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 60px;
    padding: 60px 40px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.stat-item {
    text-align: center;
    padding: 20px;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 1.1rem;
    color: var(--text-color);
    font-weight: 500;
}

.info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 40px 30px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.info-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.info-item:hover::before {
    left: 100%;
}

.info-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.info-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    transition: all 0.3s ease;
}

.info-item:hover .info-icon {
    transform: scale(1.1) rotate(5deg);
}

.info-icon i {
    font-size: 2rem;
    color: white;
}

.info-details h3 {
    color: white;
    margin-bottom: 15px;
    font-size: 1.3rem;
    font-weight: 600;
}

.info-details p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    font-size: 1.1rem;
}

.qr-code {
    margin-top: 20px;
}

.qr-code img {
    width: 120px;
    height: 120px;
    border-radius: 10px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.qr-code img:hover {
    transform: scale(1.05);
    border-color: var(--primary-color);
}

.qr-code p {
    margin-top: 10px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

/* 底部展示区域 */
.bottom-showcase {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.bottom-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.bottom-item {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    transition: all 0.4s ease;
    background: white;
}

.bottom-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.bottom-image {
    position: relative;
    height: 280px;
    overflow: hidden;
}

.bottom-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.4s ease;
}

.bottom-item:hover .bottom-image img {
    transform: scale(1.05);
}

.bottom-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 40px 25px 25px;
    transform: translateY(20px);
    transition: all 0.4s ease;
}

.bottom-item:hover .bottom-overlay {
    transform: translateY(0);
}

.bottom-content h3 {
    color: white;
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.bottom-content p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.95rem;
    line-height: 1.6;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 页脚 */
footer {
    background-color: var(--dark-color);
    color: var(--light-text);
    padding-top: 60px;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo {
    flex: 1;
    min-width: 250px;
}

.footer-logo h2 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.footer-links {
    flex: 1;
    min-width: 200px;
}

.footer-links h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-links h3::after {
    content: '';
    position: absolute;
    width: 40px;
    height: 2px;
    background-color: var(--primary-color);
    bottom: 0;
    left: 0;
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links ul li a {
    color: #aaa;
    transition: var(--transition);
}

.footer-links ul li a:hover {
    color: var(--primary-color);
    padding-left: 5px;
}

.footer-contact {
    flex: 1;
    min-width: 250px;
}

.footer-contact h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-contact h3::after {
    content: '';
    position: absolute;
    width: 40px;
    height: 2px;
    background-color: var(--primary-color);
    bottom: 0;
    left: 0;
}

/* 联系信息一行排列 */
.contact-items-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: flex-start;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    color: #aaa;
    transition: var(--transition);
    flex: 1;
    min-width: 200px;
}

.contact-item:hover {
    color: var(--primary-color);
}

.contact-item i {
    width: 20px;
    margin-right: 8px;
    margin-top: 2px;
    color: var(--primary-color);
    flex-shrink: 0;
}

.contact-item span {
    line-height: 1.5;
    font-size: 0.9rem;
}

.footer-follow {
    flex: 1;
    min-width: 250px;
}

.footer-follow h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-follow h3::after {
    content: '';
    position: absolute;
    width: 40px;
    height: 2px;
    background-color: var(--primary-color);
    bottom: 0;
    left: 0;
}

.footer-follow p {
    color: #aaa;
    margin-bottom: 20px;
    font-size: 0.95rem;
}

.qr-code-container {
    text-align: center;
}

.qr-code-container img {
    width: 120px;
    height: 120px;
    border-radius: 12px;
    border: 3px solid rgba(52, 152, 219, 0.2);
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.qr-code-container img:hover {
    transform: scale(1.05);
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.qr-code-container p {
    margin-top: 12px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 30px 0 20px;
    text-align: center;
}

.footer-certificates {
    margin-bottom: 25px;
}

.certificates-line {
    color: #aaa;
    font-size: 0.9rem;
    line-height: 1.8;
    margin-bottom: 20px;
    padding: 0 20px;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.cert-link {
    color: #aaa;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: underline;
    text-decoration-color: transparent;
    display: inline-block;
    margin: 5px 8px;
    padding: 3px 6px;
    border-radius: 4px;
}

.cert-link:hover {
    color: var(--primary-color);
    text-decoration-color: var(--primary-color);
    background-color: rgba(52, 152, 219, 0.05);
}

.certificates-icons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
}

.cert-icon {
    height: 40px;
    width: auto;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.cert-icon:hover {
    opacity: 1;
    transform: scale(1.05);
}

.footer-report {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    padding-top: 20px;
}

.report-intro {
    color: #aaa;
    font-size: 0.85rem;
    line-height: 1.6;
    margin-bottom: 10px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.report-contacts {
    color: #aaa;
    font-size: 0.85rem;
    line-height: 1.6;
    margin-bottom: 15px;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
}

.report-icons {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.report-icons a {
    display: inline-block;
    transition: all 0.3s ease;
}

.report-icons a:hover {
    transform: translateY(-2px);
}

.report-icon {
    height: 35px;
    width: auto;
    opacity: 0.7;
    transition: all 0.3s ease;
    display: block;
}

.report-icons a:hover .report-icon {
    opacity: 1;
    transform: scale(1.1);
}

/* 证书展示模态框 */
.certificate-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    justify-content: center;
    align-items: center;
    animation: fadeIn 0.3s ease;
}

.certificate-modal-content {
    position: relative;
    background-color: white;
    border-radius: 15px;
    padding: 30px;
    max-width: 90%;
    max-height: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease;
}

.certificate-close-btn {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 2rem;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
    transition: color 0.3s ease;
    z-index: 1;
}

.certificate-close-btn:hover {
    color: #333;
}

.certificate-title {
    text-align: center;
    margin-bottom: 20px;
    color: var(--text-color);
    font-size: 1.5rem;
    font-weight: 600;
}

.certificate-image-container {
    text-align: center;
    max-height: 70vh;
    overflow: auto;
}

.certificate-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 动画 */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 1200px) {
    /* 权威合作响应式 - 大平板 */
    .partners-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 25px;
    }

    .partner-item {
        padding: 25px 15px;
        min-height: 100px;
    }

    .partner-logo {
        height: 70px;
    }

    .cooperation-stats {
        padding: 50px 30px;
        gap: 25px;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .stat-label {
        font-size: 1rem;
    }
}

@media (max-width: 992px) {
    .about-content {
        flex-direction: column;
    }

    .about-images,
    .about-text {
        width: 100%;
    }

    .about-images {
        margin-bottom: 30px;
        padding-right: 0;
    }

    .about-text {
        padding-left: 0;
    }

    .about-img-main,
    .about-img-secondary {
        height: 180px;
    }

    .about-img-main,
    .about-img-secondary {
        margin-bottom: 15px;
        height: 200px;
    }

    /* 保持原有样式兼容性 */
    .about-img,
    .about-text {
        width: 100%;
    }

    .hero-content h1 {
        font-size: 2.8rem;
    }

    /* 产品详情页面响应式 - 平板 */
    .product-section {
        padding: 50px 0;
    }

    .product-detail {
        flex-direction: column;
        gap: 40px;
        margin-bottom: 50px;
        min-height: auto;
    }

    .product-detail-img,
    .product-detail-info {
        flex: none;
        width: 100%;
    }

    .product-detail-info {
        text-align: center;
        padding: 0 20px;
    }

    .product-detail-info h3 {
        font-size: 1.8rem;
    }

    .product-features {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    /* 轮播图响应式 - 平板 */
    .fullscreen-product-carousel {
        min-height: 320px;
    }

    .product-card-content {
        flex-direction: column;
        max-width: 500px;
        min-height: 280px;
    }

    .product-image {
        height: 180px;
        flex: none;
    }

    .product-info {
        padding: 30px;
        text-align: center;
    }

    .product-title {
        font-size: 1.6rem;
    }

    .product-description {
        font-size: 0.95rem;
    }

    .product-features {
        justify-content: center;
        gap: 12px;
    }

    .product-actions {
        justify-content: center;
        gap: 10px;
    }

    .carousel-nav-btn {
        width: 50px;
        height: 50px;
        font-size: 18px;
    }

    .prev-btn {
        left: 20px;
    }

    .next-btn {
        right: 20px;
    }

    /* 权威合作响应式 - 平板 */
    .partners-grid {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 20px;
    }

    .partner-item {
        padding: 20px 15px;
        min-height: 90px;
    }

    .partner-logo {
        height: 60px;
    }

    .cooperation-stats {
        grid-template-columns: repeat(2, 1fr);
        padding: 40px 25px;
        gap: 20px;
    }

    .stat-number {
        font-size: 2.2rem;
    }
}

@media (max-width: 768px) {
    nav {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 80%;
        height: calc(100vh - 70px);
        background-color: #fff;
        box-shadow: var(--box-shadow);
        transition: var(--transition);
        z-index: 1000;
    }

    nav.active {
        left: 0;
    }

    nav ul {
        flex-direction: column;
        padding: 20px;
    }

    nav ul li {
        margin: 15px 0;
    }

    .mobile-menu-btn {
        display: block;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .hero-btns {
        flex-direction: column;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .footer-bottom {
        padding: 25px 0 15px;
    }

    .certificates-icons {
        gap: 15px;
    }

    .cert-icon {
        height: 35px;
    }

    .report-icons {
        gap: 12px;
    }

    .report-icon {
        height: 30px;
    }

    .certificates-line,
    .report-intro,
    .report-contacts {
        font-size: 0.8rem;
    }

    .certificates-line {
        padding: 0 15px;
        line-height: 1.6;
    }

    .cert-link {
        margin: 3px 5px;
        padding: 2px 4px;
        font-size: 0.8rem;
    }

    /* 证书模态框响应式 - 手机 */
    .certificate-modal-content {
        padding: 20px;
        margin: 20px;
    }

    .certificate-title {
        font-size: 1.3rem;
        margin-bottom: 15px;
    }

    .certificate-close-btn {
        top: 10px;
        right: 15px;
        font-size: 1.8rem;
    }

    /* 联系信息响应式 - 手机 */
    .contact-items-row {
        flex-direction: column;
        gap: 15px;
    }

    .contact-item {
        min-width: auto;
        justify-content: flex-start;
    }

    .contact-item span {
        font-size: 0.85rem;
    }

    /* 关注我们响应式 - 手机 */
    .qr-code-container img {
        width: 100px;
        height: 100px;
    }

    /* 底部展示区域响应式 */
    .bottom-gallery {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
    }

    .bottom-image {
        height: 250px;
    }

    /* Logo响应式设计 */
    .logo h1 {
        font-size: 1.5rem;
    }

    .logo span {
        font-size: 0.75rem;
    }

    /* 产品详情页面响应式 - 手机 */
    .product-section {
        padding: 40px 0;
    }

    .product-detail {
        gap: 30px;
        margin-bottom: 40px;
    }

    .product-detail-info h3 {
        font-size: 1.6rem;
        margin-bottom: 15px;
    }

    .product-detail-info > p {
        font-size: 1rem;
        margin-bottom: 25px;
    }

    .feature-item {
        padding: 15px;
        gap: 12px;
    }

    .feature-item i {
        font-size: 1.3rem;
    }

    .feature-text h4 {
        font-size: 1rem;
    }

    .feature-text p {
        font-size: 0.9rem;
    }

    /* 全屏轮播图响应式 - 手机 */
    .carousel-header {
        padding: 60px 0 30px;
    }

    .fullscreen-product-carousel {
        min-height: 280px;
    }

    .slide-content {
        padding: 12px;
    }

    .product-card-content {
        flex-direction: column;
        max-width: 100%;
        margin: 0;
        min-height: 250px;
    }

    .product-image {
        height: 150px;
        flex: none;
    }

    .product-info {
        padding: 20px;
        text-align: center;
    }

    .product-title {
        font-size: 1.4rem;
        margin-bottom: 10px;
    }

    .product-description {
        font-size: 0.9rem;
        margin-bottom: 15px;
    }

    .product-badge {
        font-size: 0.75rem;
        padding: 5px 12px;
        margin-bottom: 12px;
    }

    .product-features {
        justify-content: center;
        gap: 8px;
        margin-bottom: 15px;
    }

    .feature-item {
        font-size: 0.75rem;
        padding: 5px 10px;
    }

    .product-actions {
        justify-content: center;
        gap: 8px;
    }

    .slide-btn {
        padding: 8px 16px;
        font-size: 0.85rem;
        min-width: 100px;
    }

    .price-item {
        margin-bottom: 6px;
    }

    .price-label {
        font-size: 0.85rem;
    }

    .price-value {
        font-size: 1rem;
    }

    .current-price {
        font-size: 1.1rem;
    }

    .carousel-nav-btn {
        width: 45px;
        height: 45px;
        font-size: 16px;
    }

    .prev-btn {
        left: 15px;
    }

    .next-btn {
        right: 15px;
    }

    .carousel-dots {
        bottom: 15px;
        gap: 6px;
        padding: 8px 12px;
        max-width: 95%;
    }

    .dot {
        width: 10px;
        height: 10px;
    }

    /* 权威合作响应式 - 手机 */
    .partners {
        padding: 80px 0;
    }

    .partners-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .partner-item {
        padding: 15px 10px;
        min-height: 80px;
    }

    .partner-logo {
        height: 50px;
    }

    .cooperation-stats {
        grid-template-columns: repeat(2, 1fr);
        padding: 30px 20px;
        gap: 15px;
        margin-top: 40px;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 0.95rem;
    }
}

@media (max-width: 480px) {
    .logo {
        flex-direction: column;
        align-items: flex-start;
    }

    .logo .img {
        margin-right: 0;
        margin-bottom: 8px;
        width: 50px;
        height: 50px;
    }

    .logo h1 {
        font-size: 1.3rem;
        line-height: 1.1;
    }

    .logo span {
        font-size: 0.7rem;
        margin-top: 1px;
    }

    /* 产品详情页面响应式 - 超小屏幕 */
    .product-section {
        padding: 30px 0;
    }

    .product-detail {
        gap: 25px;
        margin-bottom: 30px;
    }

    .product-detail-info {
        padding: 0 15px;
    }

    .product-detail-info h3 {
        font-size: 1.4rem;
        margin-bottom: 12px;
    }

    .product-detail-info > p {
        font-size: 0.95rem;
        margin-bottom: 20px;
    }

    .feature-item {
        padding: 12px;
        gap: 10px;
    }

    .feature-item i {
        font-size: 1.2rem;
    }

    .feature-text h4 {
        font-size: 0.95rem;
    }

    .feature-text p {
        font-size: 0.85rem;
    }

    /* 全屏轮播图响应式 - 超小屏幕 */
    .carousel-header {
        padding: 50px 0 20px;
    }

    .fullscreen-product-carousel {
        min-height: 250px;
    }

    .slide-content {
        padding: 8px;
    }

    .product-card-content {
        flex-direction: column;
        margin: 0;
        min-height: 220px;
    }

    .product-image {
        height: 120px;
        flex: none;
    }

    .product-info {
        padding: 15px;
        text-align: center;
    }

    .product-title {
        font-size: 1.2rem;
        margin-bottom: 8px;
    }

    .product-description {
        font-size: 0.8rem;
        margin-bottom: 12px;
    }

    .product-badge {
        font-size: 0.7rem;
        padding: 4px 10px;
        margin-bottom: 10px;
    }

    .product-features {
        justify-content: center;
        gap: 6px;
        margin-bottom: 12px;
    }

    .feature-item {
        font-size: 0.7rem;
        padding: 4px 8px;
    }

    .product-actions {
        justify-content: center;
        flex-direction: column;
        gap: 6px;
    }

    .slide-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
        min-width: 90px;
    }

    .price-item {
        margin-bottom: 5px;
    }

    .price-label {
        font-size: 0.8rem;
    }

    .price-value {
        font-size: 0.9rem;
    }

    .current-price {
        font-size: 1rem;
    }

    .carousel-nav-btn {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }

    .prev-btn {
        left: 10px;
    }

    .next-btn {
        right: 10px;
    }

    .carousel-dots {
        bottom: 10px;
        gap: 5px;
        padding: 6px 10px;
        max-width: 98%;
    }

    .dot {
        width: 8px;
        height: 8px;
    }

    /* 权威合作响应式 - 超小屏幕 */
    .partners {
        padding: 60px 0;
    }

    .partners-content {
        margin-top: 40px;
    }

    .partners-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .partner-item {
        padding: 12px 8px;
        min-height: 70px;
    }

    .partner-logo {
        height: 45px;
    }

    .cooperation-stats {
        grid-template-columns: 1fr;
        padding: 25px 15px;
        gap: 12px;
        margin-top: 30px;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .stat-label {
        font-size: 0.9rem;
    }

    /* 关于我们图片响应式 - 超小屏幕 */
    .about-img-main,
    .about-img-secondary {
        height: 150px;
    }

    /* 底部展示区域响应式 - 超小屏幕 */
    .bottom-showcase {
        padding: 60px 0;
    }

    .bottom-gallery {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-top: 30px;
    }

    .bottom-image {
        height: 200px;
    }

    .bottom-content h3 {
        font-size: 1.2rem;
    }

    .bottom-content p {
        font-size: 0.9rem;
    }

    /* 联系信息响应式 - 超小屏幕 */
    .contact-items-row {
        flex-direction: column;
        gap: 12px;
    }

    .contact-item {
        min-width: auto;
        justify-content: flex-start;
    }

    .contact-item span {
        font-size: 0.8rem;
        line-height: 1.4;
    }

    .contact-item i {
        margin-right: 6px;
        width: 16px;
    }

    /* 关注我们响应式 - 超小屏幕 */
    .qr-code-container img {
        width: 90px;
        height: 90px;
    }

    .qr-code-container p {
        font-size: 0.8rem;
        margin-top: 8px;
    }

    /* 页脚底部响应式 - 超小屏幕 */
    .footer-bottom {
        padding: 20px 0 15px;
    }

    .certificates-icons {
        gap: 10px;
        flex-wrap: wrap;
    }

    .cert-icon {
        height: 30px;
    }

    .report-icons {
        gap: 8px;
    }

    .report-icon {
        height: 25px;
    }

    .certificates-line,
    .report-intro,
    .report-contacts {
        font-size: 0.75rem;
        padding: 0 10px;
    }

    .certificates-line {
        line-height: 1.5;
        padding: 0 8px;
    }

    .cert-link {
        margin: 2px 3px;
        padding: 1px 3px;
        font-size: 0.75rem;
        display: inline-block;
    }

    /* 证书模态框响应式 - 超小屏幕 */
    .certificate-modal-content {
        padding: 15px;
        margin: 10px;
    }

    .certificate-title {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }

    .certificate-close-btn {
        top: 8px;
        right: 12px;
        font-size: 1.5rem;
    }

    .certificate-image {
        max-height: 60vh;
    }
}