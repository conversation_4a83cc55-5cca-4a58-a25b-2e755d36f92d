/* 动画效果样式 */

/* 淡入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 从下方淡入 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 从上方淡入 */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 从左侧淡入 */
@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 从右侧淡入 */
@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 放大淡入 */
@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 旋转淡入 */
@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-15deg) scale(0.9);
    }
    to {
        opacity: 1;
        transform: rotate(0) scale(1);
    }
}

/* 弹跳效果 */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

/* 脉冲效果 */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* 闪光效果 */
@keyframes flash {
    0%, 50%, 100% {
        opacity: 1;
    }
    25%, 75% {
        opacity: 0.5;
    }
}

/* 应用动画的通用类 */
.animate {
    animation-duration: 1s;
    animation-fill-mode: both;
}

/* 动画延迟类 */
.delay-1 {
    animation-delay: 0.2s;
}
.delay-2 {
    animation-delay: 0.4s;
}
.delay-3 {
    animation-delay: 0.6s;
}
.delay-4 {
    animation-delay: 0.8s;
}
.delay-5 {
    animation-delay: 1s;
}

/* 具体动画类 */
.fade-in {
    animation-name: fadeIn;
}
.fade-in-up {
    animation-name: fadeInUp;
}
.fade-in-down {
    animation-name: fadeInDown;
}
.fade-in-left {
    animation-name: fadeInLeft;
}
.fade-in-right {
    animation-name: fadeInRight;
}
.zoom-in {
    animation-name: zoomIn;
}
.rotate-in {
    animation-name: rotateIn;
}
.bounce {
    animation-name: bounce;
}
.pulse {
    animation-name: pulse;
    animation-duration: 2s;
    animation-iteration-count: infinite;
}
.flash {
    animation-name: flash;
    animation-duration: 2s;
    animation-iteration-count: infinite;
}

/* 滚动动画 - 使用JavaScript触发 */
.scroll-animation {
    opacity: 0;
    transition: all 0.8s ease;
}
.scroll-animation.active {
    opacity: 1;
}

.scroll-fade-up {
    transform: translateY(50px);
}
.scroll-fade-up.active {
    transform: translateY(0);
}

.scroll-fade-left {
    transform: translateX(-50px);
}
.scroll-fade-left.active {
    transform: translateX(0);
}

.scroll-fade-right {
    transform: translateX(50px);
}
.scroll-fade-right.active {
    transform: translateX(0);
}

.scroll-zoom {
    transform: scale(0.9);
}
.scroll-zoom.active {
    transform: scale(1);
}

/* 悬停动画效果 */
.hover-grow {
    transition: transform 0.3s ease;
}
.hover-grow:hover {
    transform: scale(1.05);
}

.hover-float {
    transition: transform 0.3s ease;
}
.hover-float:hover {
    transform: translateY(-10px);
}

.hover-shadow {
    transition: box-shadow 0.3s ease;
}
.hover-shadow:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-rotate {
    transition: transform 0.3s ease;
}
.hover-rotate:hover {
    transform: rotate(5deg);
}

/* 按钮动画 */
.btn-pulse {
    position: relative;
    overflow: hidden;
}

.btn-pulse::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120%;
    height: 120%;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    opacity: 0;
    transition: transform 0.5s, opacity 0.5s;
}

.btn-pulse:hover::after {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
} 