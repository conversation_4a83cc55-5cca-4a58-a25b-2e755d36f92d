<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>西安易复诊铭智网络科技有限公司</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="shortcut icon" href="images/favicon.ico" type="image/x-icon">
</head>

<body>
    <!-- 导航栏 -->
    <header>
        <div class="container">
            <div class="logo">
                <img class="img" src="images/logo.png" alt="logo">
                <div style="position: relative;">
                    <h1>西安易复诊铭智网络科技有限公司</h1>
                    <span>Xi'an YiFuZhen MingZhi Network Technology Co., Ltd.</span>
                </div>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html" id="nav-home" class="active">首页</a></li>
                    <li><a href="about.html" id="nav-about">关于我们</a></li>
                    <li><a href="product.html" id="nav-product">产品服务</a></li>
                    <li><a href="team.html" id="nav-team">团队介绍</a></li>
                    <li><a href="#footer-contact" id="nav-contact" onclick="scrollToContact(event)">联系我们</a></li>
                </ul>
            </nav>
            <div class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <!-- 首页横幅 -->
    <section id="home" class="hero" style="background-image: url('images/hero-bg.jpg');">
        <div class="container">
            <div class="hero-content">
                <h1 class="animate fade-in-down">智慧监管 · 服务便民</h1>
                <p class="animate fade-in-up delay-1">医保电子处方中心的流转应用及监管系统</p>
                <div class="hero-btns animate fade-in-up delay-2">
                    <a href="product.html" class="btn btn-primary">了解产品</a>
                    <a href="about.html" class="btn btn-outline">关于我们</a>
                </div>
            </div>
        </div>
    </section>

    <!-- 关于我们 -->
    <section id="about" class="about">
        <div class="container">
            <div class="section-header scroll-animation scroll-fade-up">
                <h2>关于我们</h2>
                <p>专注"互联网+医药"领域，为政府处方监管需求和患者处方服务需求提供完整的一体化服务体系</p>
            </div>
            <div class="about-content">
                <div class="about-images scroll-animation scroll-fade-right">
                    <div class="about-img-main">
                        <img src="images/about-us.jpg" alt="关于我们">
                    </div>
                    <div class="about-img-secondary">
                        <img src="images/about-yb.jpg" alt="公司业务">
                    </div>
                </div>
                <div class="about-text scroll-animation scroll-fade-left">
                    <p class="textIndent">西安易复诊铭智网络科技有限公司，成立于2017年，总部位于西安，是一家集软件开发、技术服务、项目运营为一体的科技企业。公司自成立以来，始终以"惠民"、"惠政"为核心目标，长期深耕于"互联网+医药"领域，专注"处方流转平台"、"电子处方中心"、"处方流转应用及监管系统"的研发和应用。</p>
                    <p class="textIndent">公司团队深耕行业多年，精准把握行业痛点，在全国各省市参与了多项"处方流转平台"和"医保双通道"项目建设，与政府和企业达成了长期、良好的合作关系，深得客户好评。公司现有从事软硬件开发及相关支持人员130余人，其中软件设计、开发及SQA工程师63人，实施工程师22人，售前工程师6人，其他技术支持人员30余人。</p>
                    <div class="about-features">
                        <div class="feature scroll-animation scroll-fade-up">
                            <i class="fas fa-shield-alt pulse"></i>
                            <h4>安全可靠</h4>
                            <p>ISO27001信息安全管理体系认证</p>
                        </div>
                        <div class="feature scroll-animation scroll-fade-up delay-1">
                            <i class="fas fa-award pulse"></i>
                            <h4>专业认证</h4>
                            <p>ISO9001质量管理体系认证</p>
                        </div>
                        <div class="feature scroll-animation scroll-fade-up delay-2">
                            <i class="fas fa-lightbulb pulse"></i>
                            <h4>技术创新</h4>
                            <p>多项软件著作权和技术专利</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>





    <!-- 权威合作 -->
    <section id="partners" class="partners">
        <div class="container">
            <div class="section-header scroll-animation scroll-fade-up">
                <h2>权威合作</h2>
                <p>与行业领军企业深度合作，共同推动医药信息化发展</p>
            </div>
            <div class="partners-content">
                <div class="partners-grid">
                    <div class="partner-item scroll-animation scroll-fade-up">
                        <div class="partner-logo">
                            <img src="images/partners/p1.png" alt="合作伙伴1">
                        </div>
                    </div>
                    <div class="partner-item scroll-animation scroll-fade-up delay-1">
                        <div class="partner-logo">
                            <img src="images/partners/p2.png" alt="合作伙伴2">
                        </div>
                    </div>
                    <div class="partner-item scroll-animation scroll-fade-up delay-2">
                        <div class="partner-logo">
                            <img src="images/partners/p3.png" alt="合作伙伴3">
                        </div>
                    </div>
                    <div class="partner-item scroll-animation scroll-fade-up delay-3">
                        <div class="partner-logo">
                            <img src="images/partners/p4.png" alt="合作伙伴4">
                        </div>
                    </div>
                    <div class="partner-item scroll-animation scroll-fade-up">
                        <div class="partner-logo">
                            <img src="images/partners/p5.png" alt="合作伙伴5">
                        </div>
                    </div>
                    <div class="partner-item scroll-animation scroll-fade-up delay-1">
                        <div class="partner-logo">
                            <img src="images/partners/p6.png" alt="合作伙伴6">
                        </div>
                    </div>
                    <div class="partner-item scroll-animation scroll-fade-up delay-2">
                        <div class="partner-logo">
                            <img src="images/partners/p7.png" alt="合作伙伴7">
                        </div>
                    </div>
                    <div class="partner-item scroll-animation scroll-fade-up delay-3">
                        <div class="partner-logo">
                            <img src="images/partners/p8.png" alt="合作伙伴8">
                        </div>
                    </div>
                    <div class="partner-item scroll-animation scroll-fade-up">
                        <div class="partner-logo">
                            <img src="images/partners/p9.png" alt="合作伙伴9">
                        </div>
                    </div>
                    <div class="partner-item scroll-animation scroll-fade-up delay-1">
                        <div class="partner-logo">
                            <img src="images/partners/p10.png" alt="合作伙伴10">
                        </div>
                    </div>
                    <div class="partner-item scroll-animation scroll-fade-up delay-2">
                        <div class="partner-logo">
                            <img src="images/partners/p11.png" alt="合作伙伴11">
                        </div>
                    </div>
                    <div class="partner-item scroll-animation scroll-fade-up delay-3">
                        <div class="partner-logo">
                            <img src="images/partners/p12.png" alt="合作伙伴12">
                        </div>
                    </div>
                    <div class="partner-item scroll-animation scroll-fade-up">
                        <div class="partner-logo">
                            <img src="images/partners/p13.png" alt="合作伙伴13">
                        </div>
                    </div>
                    <div class="partner-item scroll-animation scroll-fade-up delay-1">
                        <div class="partner-logo">
                            <img src="images/partners/p14.png" alt="合作伙伴14">
                        </div>
                    </div>
                    <div class="partner-item scroll-animation scroll-fade-up delay-2">
                        <div class="partner-logo">
                            <img src="images/partners/p15.png" alt="合作伙伴15">
                        </div>
                    </div>
                </div>

                <!-- 合作成果展示 -->
                <div class="cooperation-stats">
                    <div class="stat-item scroll-animation scroll-fade-up">
                        <div class="stat-number">15+</div>
                        <div class="stat-label">权威合作伙伴</div>
                    </div>
                    <div class="stat-item scroll-animation scroll-fade-up delay-1">
                        <div class="stat-number">100+</div>
                        <div class="stat-label">合作项目</div>
                    </div>
                    <div class="stat-item scroll-animation scroll-fade-up delay-2">
                        <div class="stat-number">31</div>
                        <div class="stat-label">覆盖省市</div>
                    </div>
                    <div class="stat-item scroll-animation scroll-fade-up delay-3">
                        <div class="stat-number">7年+</div>
                        <div class="stat-label">深度合作</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>西安易复诊铭智网络科技有限公司</h2>
                    <p>专注"互联网+医药"领域的科技创新企业</p>
                </div>
                <div class="footer-links">
                    <h3>快速链接</h3>
                    <ul>
                        <li><a href="index.html">首页</a></li>
                        <li><a href="about.html">关于我们</a></li>
                        <li><a href="product.html">产品服务</a></li>
                        <li><a href="team.html">团队介绍</a></li>
                        <li><a href="#footer-contact" onclick="scrollToContact(event)">联系我们</a></li>
                    </ul>
                </div>
                <div class="footer-contact" id="footer-contact">
                    <h3>联系方式</h3>
                    <div class="contact-items-row">
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>西安市未央区太华北路与凤城三路交口西南-四海中心B座612</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <span>18192283054</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-clock"></i>
                            <span>9:00-18:00</span>
                        </div>
                    </div>
                </div>
                <div class="footer-follow">
                    <h3>关注我们</h3>
                    <p>扫码关注企业公众号，获取最新资讯</p>
                    <div class="qr-code-container">
                        <img src="images/gzh.jpg" alt="企业公众号二维码">
                        <p>微信公众号</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-certificates">
                    <div class="certificates-line">
                        <a href="#" class="cert-text-link" onclick="showCertificate('images/certificate/zzry5.png', '互联网药品信息服务资格证'); return false;" title="点击查看证书">
                            互联网药品信息服务资格证编号：(陕)-经营性-2023-0024
                        </a>
                        <a href="#" class="cert-text-link" onclick="showCertificate('images/certificate/zzry2.png', '增值电信业务经营许可证'); return false;" title="点击查看证书">
                            增值电信业务经营许可证编号：陕B2-20230168
                        </a>
                        <a href="#" class="cert-text-link" onclick="showCertificate('images/certificate/zzry6.jpg', '营业执照'); return false;" title="点击查看证书">
                            营业执照
                        </a>
                    </div>
                    <div class="certificates-links">
                        <a href="https://beian.mps.gov.cn/#/query/webSearch" target="_blank" rel="noopener noreferrer" class="cert-text-link" title="点击查询公安备案信息">
                            陕公网安备61010302001119号
                        </a>
                        <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank" rel="noopener noreferrer" class="cert-text-link" title="点击查询ICP备案信息">
                            陕ICP备2023004908号-1
                        </a>
                    </div>
                </div>
                <div class="footer-report">
                    <p class="report-intro">以下渠道均可投诉举报：互联网违法和不良信息，涉及未成年人保护、互联网算法推荐、谣言类信息等相关问题。</p>
                    <p class="report-contacts">
                        陕西省网络举报中心举报电话 029-63907150 / 029-63907152丨违法和不良信息举报电话12377丨全国文化和旅游市场举报电话12318
                    </p>
                    <div class="report-icons">
                        <a href="https://www.shaanxijubao.cn/" target="_blank" rel="noopener noreferrer" title="陕西省网络举报中心">
                            <img src="images/bottom/bottom1.jpg" alt="陕西省网络举报中心" class="report-icon">
                        </a>
                        <a href="https://www.12377.cn/" target="_blank" rel="noopener noreferrer" title="中央网信办违法和不良信息举报中心">
                            <img src="images/bottom/bottom2.jpg" alt="中央网信办违法和不良信息举报中心" class="report-icon">
                        </a>
                        <a href="https://jbts.mct.gov.cn/home" target="_blank" rel="noopener noreferrer" title="全国文化和旅游市场网上举报投诉处理系统">
                            <img src="images/bottom/bottom3.jpg" alt="全国文化和旅游市场网上举报投诉处理系统" class="report-icon">
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 证书展示模态框 -->
    <div id="certificateModal" class="certificate-modal" onclick="closeCertificateModal()">
        <div class="certificate-modal-content" onclick="event.stopPropagation()">
            <span class="certificate-close-btn" onclick="closeCertificateModal()">&times;</span>
            <h3 id="certificateTitle" class="certificate-title"></h3>
            <div class="certificate-image-container">
                <img id="certificateImage" class="certificate-image" src="" alt="">
            </div>
        </div>
    </div>
    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
    <script>
        // 证书展示功能
        function showCertificate(imageSrc, title) {
            const modal = document.getElementById('certificateModal');
            const modalImg = document.getElementById('certificateImage');
            const modalTitle = document.getElementById('certificateTitle');

            modal.style.display = 'flex';
            modalImg.src = imageSrc;
            modalImg.alt = title;
            modalTitle.textContent = title;
            document.body.style.overflow = 'hidden'; // 防止背景滚动
        }

        function closeCertificateModal() {
            const modal = document.getElementById('certificateModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto'; // 恢复滚动
        }

        // 滚动到联系我们部分的函数
        function scrollToContact(event) {
            event.preventDefault();

            const contactSection = document.querySelector('.footer-contact');
            if (contactSection) {
                // 平滑滚动到联系我们部分
                contactSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // 更新URL hash
                window.history.pushState(null, null, '#footer-contact');

                // 设置导航高亮
                const navLinks = document.querySelectorAll('nav a');
                navLinks.forEach(link => link.classList.remove('active'));
                const contactLink = document.getElementById('nav-contact');
                if (contactLink) contactLink.classList.add('active');
            }
        }

        // 键盘ESC关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeCertificateModal();
            }
        });
    </script>
</body>

</html>