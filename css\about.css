/* About页面专用样式 */

.about-section {
    padding: 120px 0;
}

.about-section h2 {
    font-size: 2.2rem;
    margin-bottom: 30px;
    color: var(--dark-color);
}

.about-section p {
    margin-bottom: 20px;
    line-height: 1.8;
}

.timeline {
    position: relative;
    max-width: 800px;
    margin: 50px auto;
}

.timeline::after {
    content: '';
    position: absolute;
    width: 4px;
    background-color: var(--primary-color);
    top: 0;
    bottom: 0;
    left: 50%;
    margin-left: -2px;
}

.timeline-item {
    padding: 10px 40px;
    position: relative;
    width: 50%;
    box-sizing: border-box;
}

.timeline-item::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    background-color: #fff;
    border: 4px solid var(--primary-color);
    border-radius: 50%;
    top: 15px;
    z-index: 1;
}

.left {
    left: 0;
    text-align: right;
}

.right {
    left: 50%;
}

.left::after {
    right: -10px;
}

.right::after {
    left: -10px;
}

.timeline-content {
    padding: 20px;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: var(--box-shadow);
}

.timeline-content h3 {
    margin-bottom: 10px;
    color: var(--primary-color);
}

@media (max-width: 768px) {
    .timeline::after {
        left: 31px;
    }

    .timeline-item {
        width: 100%;
        padding-left: 70px;
        padding-right: 25px;
    }

    .timeline-item::after {
        left: 21px;
    }

    .left,
    .right {
        left: 0;
        text-align: left;
    }
}

/* 图片放大模态框样式 */
.image-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    justify-content: center;
    align-items: center;
    animation: fadeIn 0.3s ease;
}

.modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    animation: zoomIn 0.3s ease;
}

.close-btn {
    position: absolute;
    top: -40px;
    right: 0;
    color: white;
    font-size: 35px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10000;
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: #ff6b6b;
}

.image-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    transform: translateY(-50%);
    pointer-events: none;
}

.nav-btn {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    font-size: 24px;
    padding: 15px 20px;
    cursor: pointer;
    border-radius: 50%;
    transition: all 0.3s ease;
    pointer-events: auto;
    backdrop-filter: blur(10px);
}

.nav-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.prev-btn {
    margin-left: -60px;
}

.next-btn {
    margin-right: -60px;
}

/* 证书图片悬停效果 */
.certificate-img:hover {
    transform: scale(1.05);
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modal-content {
        max-width: 95%;
        max-height: 95%;
    }

    .close-btn {
        top: -30px;
        font-size: 28px;
    }

    .nav-btn {
        font-size: 20px;
        padding: 12px 16px;
    }

    .prev-btn {
        margin-left: -50px;
    }

    .next-btn {
        margin-right: -50px;
    }
}
