/* About页面专用功能 */

// 图片放大功能
let currentImageIndex = 0;
const certificateImages = [
    'images/zzry1.png',
    'images/zzry2.png',
    'images/zzry3.png',
    'images/zzry4.png'
];

function openImageModal(imgElement) {
    const modal = document.getElementById('imageModal');
    const modalImg = document.getElementById('modalImage');

    // 找到当前图片的索引
    currentImageIndex = certificateImages.findIndex(src => imgElement.src.includes(src.split('/').pop()));

    modal.style.display = 'flex';
    modalImg.src = imgElement.src;
    modalImg.alt = imgElement.alt;
    document.body.style.overflow = 'hidden'; // 防止背景滚动
}

function closeImageModal() {
    const modal = document.getElementById('imageModal');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto'; // 恢复滚动
}

function prevImage(event) {
    event.stopPropagation();
    currentImageIndex = (currentImageIndex - 1 + certificateImages.length) % certificateImages.length;
    updateModalImage();
}

function nextImage(event) {
    event.stopPropagation();
    currentImageIndex = (currentImageIndex + 1) % certificateImages.length;
    updateModalImage();
}

function updateModalImage() {
    const modalImg = document.getElementById('modalImage');
    modalImg.src = certificateImages[currentImageIndex];
    modalImg.alt = `荣誉资质证书${currentImageIndex + 1}`;
}

// 键盘导航
document.addEventListener('keydown', function(event) {
    const imageModal = document.getElementById('imageModal');
    const certificateModal = document.getElementById('certificateModal');

    if (event.key === 'Escape') {
        if (certificateModal && certificateModal.style.display === 'flex') {
            closeCertificateModal();
        } else if (imageModal && imageModal.style.display === 'flex') {
            closeImageModal();
        }
    } else if (imageModal && imageModal.style.display === 'flex') {
        if (event.key === 'ArrowLeft') {
            prevImage(event);
        } else if (event.key === 'ArrowRight') {
            nextImage(event);
        }
    }
});
