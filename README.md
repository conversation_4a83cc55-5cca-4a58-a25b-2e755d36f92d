# 医药处方科技公司官网

这是一个医药处方科技公司的官方网站项目，包含首页、关于我们、产品服务等页面。

## 项目结构

```
├── index.html          # 网站首页
├── about.html          # 关于我们页面
├── product.html        # 产品服务页面
├── css/                # CSS样式文件目录
│   └── style.css       # 主样式文件
├── js/                 # JavaScript文件目录
│   └── main.js         # 主脚本文件
└── images/             # 图片资源目录
    ├── favicon.ico     # 网站图标
    ├── hero-bg.jpg     # 首页背景图
    ├── about-us.jpg    # 关于我们图片
    ├── product-1.jpg   # 产品图片1
    ├── product-2.jpg   # 产品图片2
    ├── product-3.jpg   # 产品图片3
    ├── team-1.jpg      # 团队成员图片1
    ├── team-2.jpg      # 团队成员图片2
    ├── team-3.jpg      # 团队成员图片3
    ├── team-4.jpg      # 团队成员图片4
    └── 生成处方公司官网背景图(*.png)  # 其他背景图片
```

## 功能特点

- 响应式设计，适配各种屏幕尺寸
- 现代化UI界面，用户友好
- 平滑滚动和动画效果
- 移动端菜单支持
- 产品详情选项卡展示
- 联系表单和订阅功能

## 技术栈

- HTML5
- CSS3 (自定义变量、Flexbox、Grid等)
- JavaScript (原生JS，无框架依赖)
- Font Awesome 图标库

## 本地运行

由于网站使用了相对路径引用资源，建议通过本地Web服务器运行项目，以避免可能的跨域问题。

### 使用Python启动简易服务器

```bash
# 如果安装了Python 3
python -m http.server

# 如果安装了Python 2
python -m SimpleHTTPServer
```

然后在浏览器中访问 `http://localhost:8000`

### 使用Node.js启动简易服务器

首先安装http-server：

```bash
npm install -g http-server
```

然后在项目根目录运行：

```bash
http-server
```

然后在浏览器中访问 `http://localhost:8080`

## 浏览器兼容性

- Chrome (最新版)
- Firefox (最新版)
- Safari (最新版)
- Edge (最新版)
- 移动端浏览器

## 自定义修改

### 修改颜色主题

在 `css/style.css` 文件中，可以通过修改CSS变量来更改网站的主题颜色：

```css
:root {
    --primary-color: #3498db;    /* 主色调 */
    --secondary-color: #2ecc71;  /* 次要色调 */
    --dark-color: #2c3e50;       /* 深色 */
    --light-color: #ecf0f1;      /* 浅色 */
    /* 其他颜色变量... */
}
```

### 添加新页面

1. 复制现有HTML页面作为模板
2. 修改页面标题、内容和链接
3. 在导航栏中添加新页面的链接

## 联系方式

如有任何问题或建议，请联系：<EMAIL> 