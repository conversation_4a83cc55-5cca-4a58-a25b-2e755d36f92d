<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地服务器启动指南</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .method {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .command {
            background: #2d3748;
            color: #e2e8f0;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>🚀 本地服务器启动指南</h1>
    
    <div class="note">
        <strong>为什么需要本地服务器？</strong><br>
        由于浏览器的安全限制，直接打开HTML文件（file://协议）无法使用fetch API加载其他文件。
        页眉页脚组件需要通过HTTP协议加载才能正常工作。
    </div>

    <h2>方法1：Python服务器（推荐）</h2>
    <div class="method">
        <p><strong>如果您安装了Python：</strong></p>
        <p>1. 打开命令行/终端，进入项目目录</p>
        <p>2. 运行以下命令之一：</p>
        
        <p><strong>Python 3:</strong></p>
        <div class="command">python -m http.server 8000</div>
        
        <p><strong>Python 2:</strong></p>
        <div class="command">python -m SimpleHTTPServer 8000</div>
        
        <p>3. 在浏览器中访问：<a href="http://localhost:8000" target="_blank">http://localhost:8000</a></p>
    </div>

    <h2>方法2：Node.js服务器</h2>
    <div class="method">
        <p><strong>如果您安装了Node.js：</strong></p>
        <p>1. 全局安装http-server：</p>
        <div class="command">npm install -g http-server</div>
        
        <p>2. 在项目目录运行：</p>
        <div class="command">http-server -p 8000</div>
        
        <p>3. 在浏览器中访问：<a href="http://localhost:8000" target="_blank">http://localhost:8000</a></p>
    </div>

    <h2>方法3：Live Server (VS Code)</h2>
    <div class="method">
        <p><strong>如果您使用VS Code：</strong></p>
        <p>1. 安装"Live Server"扩展</p>
        <p>2. 右键点击index.html文件</p>
        <p>3. 选择"Open with Live Server"</p>
    </div>

    <h2>方法4：其他简单服务器</h2>
    <div class="method">
        <p><strong>PHP服务器：</strong></p>
        <div class="command">php -S localhost:8000</div>
        
        <p><strong>Ruby服务器：</strong></p>
        <div class="command">ruby -run -e httpd . -p 8000</div>
    </div>

    <div class="success">
        <strong>✅ 备用方案已启用</strong><br>
        我已经修改了includes.js文件，添加了备用内容加载功能。
        即使无法使用服务器，页眉页脚也会显示备用内容。
        但为了最佳体验，仍建议使用本地服务器。
    </div>

    <h2>🔧 故障排除</h2>
    <div class="method">
        <p><strong>如果页眉页脚仍然不显示：</strong></p>
        <ul>
            <li>检查浏览器控制台是否有错误信息（F12打开开发者工具）</li>
            <li>确保所有CSS和JS文件路径正确</li>
            <li>尝试刷新页面（Ctrl+F5强制刷新）</li>
            <li>检查includes.js文件是否正确加载</li>
        </ul>
    </div>

    <script>
        // 检测当前协议并显示提示
        if (window.location.protocol === 'file:') {
            const warning = document.createElement('div');
            warning.className = 'note';
            warning.innerHTML = '<strong>⚠️ 检测到文件协议</strong><br>您当前正在使用file://协议打开文件。建议使用上述方法之一启动本地服务器以获得最佳体验。';
            document.body.insertBefore(warning, document.body.firstChild);
        } else {
            const success = document.createElement('div');
            success.className = 'success';
            success.innerHTML = '<strong>✅ 服务器环境检测成功</strong><br>您正在使用HTTP协议，页眉页脚应该能正常加载。';
            document.body.insertBefore(success, document.body.firstChild);
        }
    </script>
</body>
</html>
