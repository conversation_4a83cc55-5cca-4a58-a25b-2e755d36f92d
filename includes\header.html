<!-- 导航栏 -->
<header>
    <div class="container">
        <div class="logo">
            <img class="img" src="images/logo.png" alt="logo">
            <div style="position: relative;">
                <h1>西安易复诊铭智网络科技有限公司</h1>
                <span>Xi'an YiFuZhen MingZhi Network Technology Co., Ltd.</span>
            </div>
        </div>
        <nav>
            <ul>
                <li><a href="index.html" id="nav-home">首页</a></li>
                <li><a href="about.html" id="nav-about">关于我们</a></li>
                <li><a href="product.html" id="nav-product">产品服务</a></li>
                <li><a href="team.html" id="nav-team">团队介绍</a></li>
                <li><a href="#footer-contact" id="nav-contact" onclick="scrollToContact(event)">联系我们</a></li>
            </ul>
        </nav>
        <div class="mobile-menu-btn">
            <i class="fas fa-bars"></i>
        </div>
    </div>
</header>

<script>
// 设置当前页面的导航高亮
document.addEventListener('DOMContentLoaded', function() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('nav a');
    
    // 移除所有active类
    navLinks.forEach(link => link.classList.remove('active'));
    
    // 根据当前页面设置active类
    if (currentPage === 'index.html' || currentPage === '') {
        document.getElementById('nav-home').classList.add('active');
    } else if (currentPage === 'about.html') {
        document.getElementById('nav-about').classList.add('active');
    } else if (currentPage === 'product.html') {
        document.getElementById('nav-product').classList.add('active');
    } else if (currentPage === 'team.html') {
        document.getElementById('nav-team').classList.add('active');
    }

    // 检查是否是联系我们锚点
    if (window.location.hash === '#footer-contact') {
        navLinks.forEach(link => link.classList.remove('active'));
        document.getElementById('nav-contact').classList.add('active');
    }
    
    // 监听hash变化，用于团队介绍页面
    window.addEventListener('hashchange', function() {
        if (currentPage === 'about.html') {
            navLinks.forEach(link => link.classList.remove('active'));
            if (window.location.hash === '#team') {
                document.getElementById('nav-team').classList.add('active');
            } else {
                document.getElementById('nav-about').classList.add('active');
            }
        }
    });
});

// 滚动到联系我们部分的函数
function scrollToContact(event) {
    event.preventDefault();

    // 等待页脚加载完成后再滚动
    setTimeout(() => {
        const contactSection = document.querySelector('.footer-contact');
        if (contactSection) {
            // 平滑滚动到联系我们部分
            contactSection.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            // 更新URL hash
            window.history.pushState(null, null, '#footer-contact');

            // 设置导航高亮
            const navLinks = document.querySelectorAll('nav a');
            navLinks.forEach(link => link.classList.remove('active'));
            document.getElementById('nav-contact').classList.add('active');


        } else {
            console.log('联系我们部分未找到，可能页脚还未加载完成');
        }
    }, 500); // 等待500ms确保页脚已加载
}
</script>
