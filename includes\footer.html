<!-- 页脚 -->
<footer>
    <div class="container">
        <div class="footer-content">
            <div class="footer-logo">
                <h2>西安易复诊铭智网络科技有限公司</h2>
                <p>专注"互联网+医药"领域的科技创新企业</p>
            </div>
            <div class="footer-links">
                <h3>快速链接</h3>
                <ul>
                    <li><a href="index.html">首页</a></li>
                    <li><a href="about.html">关于我们</a></li>
                    <li><a href="product.html">产品服务</a></li>
                    <li><a href="team.html">团队介绍</a></li>
                    <li><a href="#footer-contact" onclick="scrollToContact(event)">联系我们</a></li>
                </ul>
            </div>
            <div class="footer-contact" id="footer-contact">
                <h3>联系方式</h3>
                <div class="contact-items-row">
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>西安市未央区太华北路与凤城三路交口西南-四海中心B座612</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>18192283054</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-clock"></i>
                        <span>9:00-18:00</span>
                    </div>
                </div>
            </div>
            <div class="footer-follow">
                <h3>关注我们</h3>
                <p>扫码关注企业公众号，获取最新资讯</p>
                <div class="qr-code-container">
                    <img src="images/gzh.jpg" alt="企业公众号二维码">
                    <p>微信公众号</p>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <div class="footer-certificates">
                <div class="certificates-line">
                    <span class="cert-link" onclick="showCertificate('images/certificate/zzry5.png', '互联网药品信息服务资格证')" title="点击查看证书">互联网药品信息服务资格证编号：(陕)-经营性-2023-0024</span>
                    <span class="cert-link" onclick="showCertificate('images/certificate/zzry2.png', '增值电信业务经营许可证')" title="点击查看证书">增值电信业务经营性许可证编号：陕B2-20230168</span>
                    <span class="cert-link" onclick="showCertificate('images/certificate/zzry6.jpg', '营业执照')" title="点击查看证书">营业执照</span>
                </div>
                <div class="certificates-icons">
                    <img src="images/bottom/bottom1.jpg" alt="陕公网安备61010302001119号" class="cert-icon">
                    <img src="images/bottom/bottom2.jpg" alt="陕ICP备2023004908号-1" class="cert-icon">
                </div>
            </div>
            <div class="footer-report">
                <p class="report-intro">以下渠道均可投诉举报：互联网违法和不良信息，涉及未成年人保护、互联网算法推荐、谣言类信息等相关问题。</p>
                <p class="report-contacts">
                    陕西省网络举报中心举报电话 029-63907150 / 029-63907152丨违法和不良信息举报电话12377丨全国文化和旅游市场举报电话12318
                </p>
                <div class="report-icons">
                    <a href="https://www.shaanxijubao.cn/" target="_blank" rel="noopener noreferrer" title="陕西省网络举报中心">
                        <img src="images/bottom/bottom1.jpg" alt="陕西省网络举报中心" class="report-icon">
                    </a>
                    <a href="https://www.12377.cn/" target="_blank" rel="noopener noreferrer" title="中央网信办违法和不良信息举报中心">
                        <img src="images/bottom/bottom2.jpg" alt="中央网信办违法和不良信息举报中心" class="report-icon">
                    </a>
                    <a href="https://jbts.mct.gov.cn/home" target="_blank" rel="noopener noreferrer" title="全国文化和旅游市场网上举报投诉处理系统">
                        <img src="images/bottom/bottom3.jpg" alt="全国文化和旅游市场网上举报投诉处理系统" class="report-icon">
                    </a>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- 证书展示模态框 -->
<div id="certificateModal" class="certificate-modal" onclick="closeCertificateModal()">
    <div class="certificate-modal-content" onclick="event.stopPropagation()">
        <span class="certificate-close-btn" onclick="closeCertificateModal()">&times;</span>
        <h3 id="certificateTitle" class="certificate-title"></h3>
        <div class="certificate-image-container">
            <img id="certificateImage" class="certificate-image" src="" alt="">
        </div>
    </div>
</div>

<script>
// 证书展示功能
function showCertificate(imageSrc, title) {
    const modal = document.getElementById('certificateModal');
    const modalImg = document.getElementById('certificateImage');
    const modalTitle = document.getElementById('certificateTitle');
    
    modal.style.display = 'flex';
    modalImg.src = imageSrc;
    modalImg.alt = title;
    modalTitle.textContent = title;
    document.body.style.overflow = 'hidden'; // 防止背景滚动
}

function closeCertificateModal() {
    const modal = document.getElementById('certificateModal');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto'; // 恢复滚动
}

// 键盘ESC关闭模态框
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeCertificateModal();
    }
});
</script>
