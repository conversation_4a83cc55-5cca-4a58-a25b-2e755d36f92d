// 滚动动画效果
document.addEventListener('DOMContentLoaded', function() {
    // 获取所有带有滚动动画类的元素
    const scrollElements = document.querySelectorAll('.scroll-animation');
    
    // 检查元素是否在视口中
    const elementInView = (el, dividend = 1) => {
        const elementTop = el.getBoundingClientRect().top;
        return (
            elementTop <= (window.innerHeight || document.documentElement.clientHeight) / dividend
        );
    };
    
    // 添加动画类
    const displayScrollElement = (element) => {
        element.classList.add('active');
    };
    
    // 处理滚动动画
    const handleScrollAnimation = () => {
        scrollElements.forEach((el) => {
            if (elementInView(el, 1.25)) {
                displayScrollElement(el);
            }
        });
    };
    
    // 添加滚动事件监听
    window.addEventListener('scroll', () => {
        handleScrollAnimation();
    });
    
    // 初始检查
    handleScrollAnimation();
    
    // 数字增长动画
    const numberElements = document.querySelectorAll('.animate-number');
    
    numberElements.forEach(el => {
        const finalNumber = parseInt(el.getAttribute('data-number'));
        const duration = parseInt(el.getAttribute('data-duration') || 2000);
        const startNumber = parseInt(el.getAttribute('data-start') || 0);
        let startTime = null;
        
        function animateNumber(timestamp) {
            if (!startTime) startTime = timestamp;
            const progress = Math.min((timestamp - startTime) / duration, 1);
            const currentNumber = Math.floor(startNumber + progress * (finalNumber - startNumber));
            el.textContent = currentNumber.toLocaleString();
            
            if (progress < 1) {
                window.requestAnimationFrame(animateNumber);
            }
        }
        
        // 当元素进入视口时开始动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    window.requestAnimationFrame(animateNumber);
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });
        
        observer.observe(el);
    });
    
    // 打字机效果
    const typeElements = document.querySelectorAll('.typewriter');
    
    typeElements.forEach(el => {
        const text = el.getAttribute('data-text');
        const speed = parseInt(el.getAttribute('data-speed') || 100);
        let index = 0;
        
        function typeWriter() {
            if (index < text.length) {
                el.textContent += text.charAt(index);
                index++;
                setTimeout(typeWriter, speed);
            }
        }
        
        // 当元素进入视口时开始动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    el.textContent = '';
                    typeWriter();
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });
        
        observer.observe(el);
    });
    
    // 添加悬停动画效果
    const productCards = document.querySelectorAll('.product-card');
    productCards.forEach(card => {
        card.classList.add('hover-float', 'hover-shadow');
    });
    
    const teamMembers = document.querySelectorAll('.member');
    teamMembers.forEach(member => {
        member.classList.add('hover-float', 'hover-shadow');
    });
    
    const features = document.querySelectorAll('.feature');
    features.forEach(feature => {
        feature.classList.add('hover-grow');
    });
    
    // 添加按钮动画效果
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(btn => {
        btn.classList.add('btn-pulse');
    });
}); 