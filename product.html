<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品服务 - 医药处方科技</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="shortcut icon" href="images/favicon.ico" type="image/x-icon">
    <style>
        .product-section-head{
            padding: 150px 0;
        }
        .product-section {
            padding: 5px 0;
        }
        
        .product-section h2 {
            font-size: 2.2rem;
            margin-bottom: 30px;
            color: var(--dark-color);
        }
        
        .product-detail {
            display: flex;
            flex-wrap: wrap;
            gap: 50px;
            margin-bottom: 80px;
        }
        
        .product-detail-img {
            flex: 1;
            min-width: 300px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--box-shadow);
        }
        
        .product-detail-img img {
            width: 100%;
            height: auto;
        }
        
        .product-detail-info {
            flex: 1;
            min-width: 300px;
        }
        
        .product-detail-info h3 {
            font-size: 1.8rem;
            margin-bottom: 20px;
            color: var(--primary-color);
        }
        
        .product-detail-info p {
            margin-bottom: 20px;
            line-height: 1.8;
        }
        
        .product-features {
            margin-top: 30px;
        }
        
        .feature-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 20px;
        }
        
        .feature-item i {
            color: var(--primary-color);
            font-size: 1.2rem;
            margin-right: 15px;
            margin-top: 3px;
        }
        
        .feature-text h4 {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }
        
        .product-cta {
            background-color: #f9f9f9;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-top: 40px;
        }
        
        .product-cta h3 {
            margin-bottom: 15px;
        }
        
        .product-tabs {
            margin-top: 50px;
        }
        
        .tab-buttons {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 30px;
        }
        
        .tab-btn {
            padding: 15px 30px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            position: relative;
        }
        
        .tab-btn.active {
            color: var(--primary-color);
        }
        
        .tab-btn.active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: var(--primary-color);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }

        .fullscreen-product-carousel {
            position: relative;
            height: 50vh;
            overflow: hidden;
        }

        .carousel-slides {
            position: relative;
            height: 100%;
        }

        .carousel-slide {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transform: scale(1.1);
            transition: all 0.8s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .carousel-slide.active {
            opacity: 1;
            transform: scale(1);
        }

        .slide-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            gap: 60px;
            margin-top: 0;
        }

        .product-card-content {
            display: flex;
            align-items: center;
            gap: 60px;
            width: 100%;
        }

        .product-image {
            flex: 1;
            max-width: 500px;
        }

        .product-image img {
            width: 100%;
            height: auto;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease;
        }

        .product-image img:hover {
            transform: scale(1.05);
        }

        .product-info {
            flex: 1;
            color: white;
            padding: 40px;
        }

        .product-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .product-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            line-height: 1.2;
        }

        .product-description {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .product-pricing {
            margin-bottom: 40px;
        }

        .price-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .price-label {
            font-size: 1.1rem;
            opacity: 0.8;
        }

        .price-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: #ffd700;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .market-price {
            text-decoration: line-through;
            opacity: 0.7;
            font-size: 1.4rem;
        }

        .current-price {
            color: #00ff88;
        }

        .product-actions {
            margin-top: 30px;
        }

        .slide-btn {
            padding: 15px 30px;
            font-size: 1.1rem;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .slide-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }

        /* 导航按钮 */
        .carousel-nav-btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            z-index: 20;
        }

        .carousel-nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-50%) scale(1.1);
        }

        .prev-btn {
            left: 30px;
        }

        .next-btn {
            right: 30px;
        }

        /* 指示器 */
        .carousel-dots {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 20;
        }

        .dot {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .dot:hover,
        .dot.active {
            background: white;
            transform: scale(1.2);
        }

        .dot::after {
            content: attr(title);
            position: absolute;
            bottom: 25px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }

        .dot:hover::after {
            opacity: 1;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .slide-content {
                flex-direction: column;
                gap: 30px;
                padding: 0 15px;
                margin-top: 20px;
            }

            .product-card-content {
                flex-direction: column;
                gap: 30px;
            }

            .product-image {
                max-width: 300px;
            }

            .product-info {
                padding: 20px;
                text-align: center;
            }

            .product-title {
                font-size: 1.8rem;
            }

            .product-description {
                font-size: 1rem;
            }

            .price-value {
                font-size: 1.4rem;
            }

            .carousel-nav-btn {
                width: 50px;
                height: 50px;
                font-size: 1.2rem;
            }

            .prev-btn {
                left: 15px;
            }

            .next-btn {
                right: 15px;
            }

            .carousel-dots {
                bottom: 20px;
                gap: 10px;
            }

            .dot {
                width: 12px;
                height: 12px;
            }
        }

        @media (max-width: 480px) {
            .product-title {
                font-size: 1.5rem;
            }

            .product-description {
                font-size: 0.9rem;
            }

            .slide-content {
                margin-top: 10px;
            }

            .product-info {
                padding: 15px;
            }
        }

        .product-card {
            background: #fff;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .product-image {
            position: relative;
            height: 250px;
            overflow: hidden;
        }

        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-card:hover .product-image img {
            transform: scale(1.1);
        }

        .product-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 123, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .product-card:hover .product-overlay {
            opacity: 1;
        }

        .product-info {
            padding: 25px;
        }

        .product-badge {
            display: inline-block;
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .product-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .product-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
            font-size: 0.95rem;
        }

        .product-pricing {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .price-label {
            color: #666;
            font-size: 0.9rem;
        }

        .price-value {
            color: var(--primary-color);
            font-size: 1.4rem;
            font-weight: 700;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .products-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .product-card {
                margin: 0 10px;
            }

            .product-image {
                height: 200px;
            }

            .product-info {
                padding: 20px;
            }

            .product-title {
                font-size: 1.1rem;
            }
        }

        /* 产品统计样式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            text-align: center;
        }

        .stat-item {
            padding: 20px;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .stat-label {
            font-size: 1.1rem;
            font-weight: 500;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }

            .stat-number {
                font-size: 2.5rem;
            }

            .stat-label {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 页眉占位符 -->
    <div id="header-placeholder"></div>
    <!-- 系统解决方案 -->
    <section class="product-section-head" style="background-color: #f9f9f9;">
        <div class="container">
            <div class="section-header scroll-animation scroll-fade-up">
                <h2>系统解决方案</h2>
                <p>专业的医药信息化系统，为医疗机构提供全方位技术支持</p>
            </div>

            <!-- 双通道服务监管系统 -->
            <div class="product-detail">
                <div class="product-detail-img remove-white-bg scroll-animation scroll-fade-right">
                    <img src="images/project/project-1.png" alt="双通道服务监管系统">
                </div>
                <div class="product-detail-info scroll-animation scroll-fade-left">
                    <h3>双通道服务监管系统</h3>
                    <p>专为医疗机构设计的双通道服务监管系统，实现对院内外处方流转的全程监管，确保药品配送和使用安全。系统采用先进的数据分析技术，对处方流转全过程进行实时监控，有效防范风险，提高服务质量。</p>
                    <div class="product-features">
                        <div class="feature-item scroll-animation scroll-fade-left">
                            <i class="fas fa-shield-alt"></i>
                            <div class="feature-text">
                                <h4>全程监管</h4>
                                <p>对院内外处方流转全过程实施监管，确保合规性</p>
                            </div>
                        </div>
                        <div class="feature-item scroll-animation scroll-fade-left delay-1">
                            <i class="fas fa-chart-line"></i>
                            <div class="feature-text">
                                <h4>数据分析</h4>
                                <p>智能分析处方数据，发现异常并及时预警</p>
                            </div>
                        </div>
                        <div class="feature-item scroll-animation scroll-fade-left delay-2">
                            <i class="fas fa-exclamation-triangle"></i>
                            <div class="feature-text">
                                <h4>风险防控</h4>
                                <p>多维度风险评估，构建药品安全防护网</p>
                            </div>
                        </div>
                        <div class="feature-item scroll-animation scroll-fade-left delay-3">
                            <i class="fas fa-arrow-up"></i>
                            <div class="feature-text">
                                <h4>质量提升</h4>
                                <p>通过数据反馈持续优化服务流程，提高患者满意度</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 药店控费自纠自查辅助交互系统 -->
    <section id="product-2" class="product-section">
        <div class="container">
            <div class="product-detail">
                <div class="product-detail-info scroll-animation scroll-fade-right">
                    <h3>药店控费自纠自查辅助交互系统</h3>
                    <p>为药店提供的智能化控费管理解决方案，帮助药店实现自纠自查，提高经营效率和合规性。系统整合药品采购、销售、库存等数据，通过智能算法分析成本结构，发现潜在浪费点，提供精准的控费建议。</p>
                    <div class="product-features">
                        <div class="feature-item">
                            <i class="fas fa-calculator"></i>
                            <div class="feature-text">
                                <h4>成本分析</h4>
                                <p>多维度分析药店运营成本，发现优化空间</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-search"></i>
                            <div class="feature-text">
                                <h4>自动检查</h4>
                                <p>自动识别异常费用支出，提供自查清单</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-comments"></i>
                            <div class="feature-text">
                                <h4>交互辅导</h4>
                                <p>通过交互式界面引导药店完成自查流程</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-lightbulb"></i>
                            <div class="feature-text">
                                <h4>优化建议</h4>
                                <p>基于数据分析提供个性化控费策略和建议</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="product-detail-img remove-white-bg scroll-animation scroll-fade-left">
                    <img src="images/project/project-2.png" alt="药店控费自纠自查辅助交互系统">
                </div>
            </div>
        </div>
    </section>

    <!-- 处方流转一站式服务系统 -->
    <section id="product-3" class="product-section">
        <div class="container">
            <div class="product-detail">
                <div class="product-detail-img remove-white-bg scroll-animation scroll-fade-right">
                    <img src="images/project/project-3.png" alt="处方流转一站式服务系统">
                </div>
                <div class="product-detail-info scroll-animation scroll-fade-left">
                    <h3>处方流转一站式服务系统</h3>
                    <p>整合医院、药店、配送等多方资源，实现处方从开具到配送的全流程数字化管理。系统打通处方开具、审核、调配、配送等环节，为患者提供便捷、安全的一站式用药服务，同时为医疗机构提供完整的处方流转管理工具。</p>
                    <div class="product-features">
                        <div class="feature-item">
                            <i class="fas fa-cogs"></i>
                            <div class="feature-text">
                                <h4>全流程管理</h4>
                                <p>覆盖处方开具、审核、调配、配送全过程</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-handshake"></i>
                            <div class="feature-text">
                                <h4>多方协作</h4>
                                <p>打通医院、药店、配送等多方信息系统</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-brain"></i>
                            <div class="feature-text">
                                <h4>智能匹配</h4>
                                <p>根据患者需求智能匹配最优药店和配送方案</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-route"></i>
                            <div class="feature-text">
                                <h4>数据追踪</h4>
                                <p>处方全生命周期可追溯，确保用药安全</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚占位符 -->
    <div id="footer-placeholder"></div>

    <script src="js/includes.js"></script>
    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
    <script>
        // 全屏产品轮播图功能
        let currentSlideIndex = 0;
        const slides = document.querySelectorAll('.carousel-slide');
        const dots = document.querySelectorAll('.dot');
        const totalSlides = slides.length;
        let autoSlideInterval;

        // 显示指定的幻灯片
        function showSlide(index) {
            // 移除所有活动状态
            slides.forEach(slide => {
                slide.classList.remove('active');
                slide.style.opacity = '0';
                slide.style.transform = 'scale(1.1)';
            });
            dots.forEach(dot => dot.classList.remove('active'));

            // 添加活动状态到当前幻灯片
            setTimeout(() => {
                slides[index].classList.add('active');
                slides[index].style.opacity = '1';
                slides[index].style.transform = 'scale(1)';
                dots[index].classList.add('active');
            }, 100);
        }

        // 切换幻灯片
        function changeSlide(direction) {
            currentSlideIndex += direction;

            if (currentSlideIndex >= totalSlides) {
                currentSlideIndex = 0;
            } else if (currentSlideIndex < 0) {
                currentSlideIndex = totalSlides - 1;
            }

            showSlide(currentSlideIndex);
            resetAutoSlide();
        }

        // 跳转到指定幻灯片
        function goToSlide(index) {
            currentSlideIndex = index;
            showSlide(currentSlideIndex);
            resetAutoSlide();
        }

        // 自动轮播
        function autoSlide() {
            currentSlideIndex = (currentSlideIndex + 1) % totalSlides;
            showSlide(currentSlideIndex);
        }

        // 开始自动轮播
        function startAutoSlide() {
            autoSlideInterval = setInterval(autoSlide, 5000); // 每5秒切换一次
        }

        // 停止自动轮播
        function stopAutoSlide() {
            clearInterval(autoSlideInterval);
        }

        // 重置自动轮播
        function resetAutoSlide() {
            stopAutoSlide();
            startAutoSlide();
        }

        // 页面加载完成后初始化轮播图
        document.addEventListener('DOMContentLoaded', function () {
            // 初始化第一张幻灯片
            if (slides.length > 0) {
                showSlide(0);

                // 开始自动轮播
                startAutoSlide();

                // 鼠标悬停时暂停自动轮播
                const carousel = document.querySelector('.fullscreen-product-carousel');
                if (carousel) {
                    carousel.addEventListener('mouseenter', stopAutoSlide);
                    carousel.addEventListener('mouseleave', startAutoSlide);
                }

                // 键盘导航支持
                document.addEventListener('keydown', function (event) {
                    if (event.key === 'ArrowLeft') {
                        changeSlide(-1);
                    } else if (event.key === 'ArrowRight') {
                        changeSlide(1);
                    }
                });

                // 触摸滑动支持（移动端）
                let startX = 0;
                let endX = 0;

                carousel.addEventListener('touchstart', function (event) {
                    startX = event.touches[0].clientX;
                });

                carousel.addEventListener('touchend', function (event) {
                    endX = event.changedTouches[0].clientX;
                    handleSwipe();
                });

                function handleSwipe() {
                    const swipeThreshold = 50;
                    const diff = startX - endX;

                    if (Math.abs(diff) > swipeThreshold) {
                        if (diff > 0) {
                            changeSlide(1); // 向左滑动，显示下一张
                        } else {
                            changeSlide(-1); // 向右滑动，显示上一张
                        }
                    }
                }
            }
        });
    </script>
</body>
</html> 