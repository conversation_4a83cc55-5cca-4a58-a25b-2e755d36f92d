/* Product页面专用样式 */

.product-section-head{
    padding: 150px 0;
}

.product-section {
    padding: 5px 0;
}

.product-section h2 {
    font-size: 2.2rem;
    margin-bottom: 30px;
    color: var(--dark-color);
}

.product-detail {
    display: flex;
    flex-wrap: wrap;
    gap: 50px;
    margin-bottom: 80px;
}

.product-detail-img {
    flex: 1;
    min-width: 300px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.product-detail-img img {
    width: 100%;
    height: auto;
}

.product-detail-info {
    flex: 1;
    min-width: 300px;
}

.product-detail-info h3 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.product-detail-info p {
    margin-bottom: 20px;
    line-height: 1.8;
}

.product-features {
    margin-top: 30px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
}

.feature-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-right: 15px;
    margin-top: 3px;
}

.feature-text h4 {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.product-cta {
    background-color: #f9f9f9;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    margin-top: 40px;
}

.product-cta h3 {
    margin-bottom: 15px;
}

.product-tabs {
    margin-top: 50px;
}

.tab-buttons {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 30px;
}

.tab-btn {
    padding: 15px 30px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: 600;
    position: relative;
}

.tab-btn.active {
    color: var(--primary-color);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.fullscreen-product-carousel {
    position: relative;
    height: 50vh;
    overflow: hidden;
}

.carousel-slides {
    position: relative;
    height: 100%;
}

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: scale(1.1);
    transition: all 0.8s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-slide.active {
    opacity: 1;
    transform: scale(1);
}

.slide-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    gap: 60px;
    margin-top: 0;
}

.product-card-content {
    display: flex;
    align-items: center;
    gap: 60px;
    width: 100%;
}

.product-image {
    flex: 1;
    max-width: 500px;
}

.product-image img {
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.product-image img:hover {
    transform: scale(1.05);
}

.product-info {
    flex: 1;
    color: white;
    padding: 40px;
}

.product-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 20px;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
}

.product-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    line-height: 1.2;
}

.product-description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 30px;
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.product-pricing {
    margin-bottom: 40px;
}

.price-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.price-label {
    font-size: 1.1rem;
    opacity: 0.8;
}

.price-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #ffd700;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.market-price {
    text-decoration: line-through;
    opacity: 0.7;
    font-size: 1.4rem;
}

.current-price {
    color: #00ff88;
}

.product-actions {
    margin-top: 30px;
}

.slide-btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.slide-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

/* 导航按钮 */
.carousel-nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    z-index: 20;
}

.carousel-nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%) scale(1.1);
}

.prev-btn {
    left: 30px;
}

.next-btn {
    right: 30px;
}

/* 指示器 */
.carousel-dots {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
    z-index: 20;
}

.dot {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.4);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.dot:hover,
.dot.active {
    background: white;
    transform: scale(1.2);
}

.dot::after {
    content: attr(title);
    position: absolute;
    bottom: 25px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.dot:hover::after {
    opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .slide-content {
        flex-direction: column;
        gap: 30px;
        padding: 0 15px;
        margin-top: 20px;
    }

    .product-card-content {
        flex-direction: column;
        gap: 30px;
    }

    .product-image {
        max-width: 300px;
    }

    .product-info {
        padding: 20px;
        text-align: center;
    }

    .product-title {
        font-size: 1.8rem;
    }

    .product-description {
        font-size: 1rem;
    }

    .price-value {
        font-size: 1.4rem;
    }

    .carousel-nav-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .prev-btn {
        left: 15px;
    }

    .next-btn {
        right: 15px;
    }

    .carousel-dots {
        bottom: 20px;
        gap: 10px;
    }

    .dot {
        width: 12px;
        height: 12px;
    }
}

@media (max-width: 480px) {
    .product-title {
        font-size: 1.5rem;
    }

    .product-description {
        font-size: 0.9rem;
    }

    .slide-content {
        margin-top: 10px;
    }

    .product-info {
        padding: 15px;
    }
}

.product-card {
    background: #fff;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.product-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 123, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-info {
    padding: 25px;
}

.product-badge {
    display: inline-block;
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.product-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 10px;
    line-height: 1.4;
}

.product-description {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
    font-size: 0.95rem;
}

.product-pricing {
    display: flex;
    align-items: center;
    gap: 10px;
}

.price-label {
    color: #666;
    font-size: 0.9rem;
}

.price-value {
    color: var(--primary-color);
    font-size: 1.4rem;
    font-weight: 700;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .products-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .product-card {
        margin: 0 10px;
    }

    .product-image {
        height: 200px;
    }

    .product-info {
        padding: 20px;
    }

    .product-title {
        font-size: 1.1rem;
    }
}

/* 产品统计样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    text-align: center;
}

.stat-item {
    padding: 20px;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
    font-size: 1.1rem;
    font-weight: 500;
    opacity: 0.9;
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .stat-label {
        font-size: 1rem;
    }
}
