/* 通用页面功能 */

// 证书展示功能
function showCertificate(imageSrc, title) {
    const modal = document.getElementById('certificateModal');
    const modalImg = document.getElementById('certificateImage');
    const modalTitle = document.getElementById('certificateTitle');
    
    modal.style.display = 'flex';
    modalImg.src = imageSrc;
    modalImg.alt = title;
    modalTitle.textContent = title;
    document.body.style.overflow = 'hidden'; // 防止背景滚动
}

function closeCertificateModal() {
    const modal = document.getElementById('certificateModal');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto'; // 恢复滚动
}

// 滚动到联系我们部分的函数
function scrollToContact(event) {
    event.preventDefault();
    
    const contactSection = document.querySelector('.footer-contact');
    if (contactSection) {
        // 平滑滚动到联系我们部分
        contactSection.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });

        // 更新URL hash
        window.history.pushState(null, null, '#footer-contact');

        // 设置导航高亮
        const navLinks = document.querySelectorAll('nav a');
        navLinks.forEach(link => link.classList.remove('active'));
        const contactLink = document.getElementById('nav-contact');
        if (contactLink) contactLink.classList.add('active');
    }
}

// 键盘ESC关闭模态框
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const certificateModal = document.getElementById('certificateModal');
        const imageModal = document.getElementById('imageModal');
        
        if (certificateModal && certificateModal.style.display === 'flex') {
            closeCertificateModal();
        } else if (imageModal && imageModal.style.display === 'flex') {
            closeImageModal();
        }
    }
});
