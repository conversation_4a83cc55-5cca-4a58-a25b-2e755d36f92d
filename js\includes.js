// 页面包含功能
document.addEventListener('DOMContentLoaded', function() {
    // 加载页眉
    loadInclude('header-placeholder', 'includes/header.html');
    
    // 加载页脚
    loadInclude('footer-placeholder', 'includes/footer.html');
});

function loadInclude(elementId, filePath) {
    const element = document.getElementById(elementId);
    if (element) {
        // 检查是否在本地文件系统中运行
        if (window.location.protocol === 'file:') {
            console.warn('检测到文件协议，页眉页脚可能无法正常加载。请使用本地服务器运行。');
            loadFallbackContent(elementId);
            return;
        }

        fetch(filePath)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text();
            })
            .then(data => {
                element.innerHTML = data;

                // 如果是页眉，执行导航高亮脚本
                if (elementId === 'header-placeholder') {
                    executeHeaderScripts();
                }

                // 如果是页脚，执行证书展示脚本
                if (elementId === 'footer-placeholder') {
                    executeFooterScripts();
                }
            })
            .catch(error => {
                console.error('Error loading include:', error);
                loadFallbackContent(elementId);
            });
    }
}

// 备用内容加载函数
function loadFallbackContent(elementId) {
    const element = document.getElementById(elementId);
    if (!element) return;

    if (elementId === 'header-placeholder') {
        element.innerHTML = getHeaderFallback();
        executeHeaderScripts();
    } else if (elementId === 'footer-placeholder') {
        element.innerHTML = getFooterFallback();
        executeFooterScripts();
    }
}

function executeHeaderScripts() {
    // 设置当前页面的导航高亮
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('nav a');
    
    // 移除所有active类
    navLinks.forEach(link => link.classList.remove('active'));
    
    // 根据当前页面设置active类
    if (currentPage === 'index.html' || currentPage === '') {
        const homeLink = document.getElementById('nav-home');
        if (homeLink) homeLink.classList.add('active');
    } else if (currentPage === 'about.html') {
        const aboutLink = document.getElementById('nav-about');
        if (aboutLink) aboutLink.classList.add('active');
    } else if (currentPage === 'product.html') {
        const productLink = document.getElementById('nav-product');
        if (productLink) productLink.classList.add('active');
    } else if (currentPage === 'team.html') {
        const teamLink = document.getElementById('nav-team');
        if (teamLink) teamLink.classList.add('active');
    }

    // 检查是否是联系我们锚点
    if (window.location.hash === '#footer-contact') {
        navLinks.forEach(link => link.classList.remove('active'));
        const contactLink = document.getElementById('nav-contact');
        if (contactLink) contactLink.classList.add('active');
    }
    
    // 监听hash变化，用于联系我们页面
    window.addEventListener('hashchange', function() {
        navLinks.forEach(link => link.classList.remove('active'));

        if (window.location.hash === '#footer-contact') {
            const contactLink = document.getElementById('nav-contact');
            if (contactLink) contactLink.classList.add('active');
        } else {
            // 恢复默认页面高亮
            if (currentPage === 'index.html' || currentPage === '') {
                const homeLink = document.getElementById('nav-home');
                if (homeLink) homeLink.classList.add('active');
            } else if (currentPage === 'about.html') {
                const aboutLink = document.getElementById('nav-about');
                if (aboutLink) aboutLink.classList.add('active');
            } else if (currentPage === 'product.html') {
                const productLink = document.getElementById('nav-product');
                if (productLink) productLink.classList.add('active');
            } else if (currentPage === 'team.html') {
                const teamLink = document.getElementById('nav-team');
                if (teamLink) teamLink.classList.add('active');
            }
        }
    });
}

function executeFooterScripts() {
    // 证书展示功能已经在footer.html中定义
    // 这里可以添加其他页脚相关的初始化代码

    // 确保证书展示功能可用
    if (typeof showCertificate === 'undefined') {
        window.showCertificate = function(imageSrc, title) {
            const modal = document.getElementById('certificateModal');
            const modalImg = document.getElementById('certificateImage');
            const modalTitle = document.getElementById('certificateTitle');

            if (modal && modalImg && modalTitle) {
                modal.style.display = 'flex';
                modalImg.src = imageSrc;
                modalImg.alt = title;
                modalTitle.textContent = title;
                document.body.style.overflow = 'hidden';
            }
        };

        window.closeCertificateModal = function() {
            const modal = document.getElementById('certificateModal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        };

        // 键盘ESC关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeCertificateModal();
            }
        });
    }
}

// 获取页眉备用内容
function getHeaderFallback() {
    return `
    <!-- 导航栏 -->
    <header>
        <div class="container">
            <div class="logo">
                <img class="img" src="images/logo.png" alt="logo">
                <div style="position: relative;">
                    <h1>西安易复诊铭智网络科技有限公司</h1>
                    <span>Xi'an YiFuZhen MingZhi Network Technology Co., Ltd.</span>
                </div>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html" id="nav-home">首页</a></li>
                    <li><a href="about.html" id="nav-about">关于我们</a></li>
                    <li><a href="product.html" id="nav-product">产品服务</a></li>
                    <li><a href="team.html" id="nav-team">团队介绍</a></li>
                    <li><a href="#footer-contact" id="nav-contact" onclick="scrollToContact(event)">联系我们</a></li>
                </ul>
            </nav>
            <div class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>
    `;
}

// 获取页脚备用内容
function getFooterFallback() {
    return `
    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>西安易复诊铭智网络科技有限公司</h2>
                    <p>专注"互联网+医药"领域的科技创新企业</p>
                </div>
                <div class="footer-links">
                    <h3>快速链接</h3>
                    <ul>
                        <li><a href="index.html">首页</a></li>
                        <li><a href="about.html">关于我们</a></li>
                        <li><a href="product.html">产品服务</a></li>
                        <li><a href="team.html">团队介绍</a></li>
                        <li><a href="#footer-contact" onclick="scrollToContact(event)">联系我们</a></li>
                    </ul>
                </div>
                <div class="footer-contact" id="footer-contact">
                    <h3>联系方式</h3>
                    <div class="contact-items-row">
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>西安市未央区太华北路与凤城三路交口西南-四海中心B座612</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <span>18192283054</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-clock"></i>
                            <span>9:00-18:00</span>
                        </div>
                    </div>
                </div>
                <div class="footer-follow">
                    <h3>关注我们</h3>
                    <p>扫码关注企业公众号，获取最新资讯</p>
                    <div class="qr-code-container">
                        <img src="images/gzh.jpg" alt="企业公众号二维码">
                        <p>微信公众号</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-certificates">
                    <div class="certificates-line">
                        <span class="cert-link" onclick="showCertificate('images/certificate/zzry5.png', '互联网药品信息服务资格证')" title="点击查看证书">互联网药品信息服务资格证编号：(陕)-经营性-2023-0024</span>
                        <span class="cert-link" onclick="showCertificate('images/certificate/zzry2.png', '增值电信业务经营许可证')" title="点击查看证书">增值电信业务经营性许可证编号：陕B2-20230168</span>
                        <span class="cert-link" onclick="showCertificate('images/certificate/zzry6.jpg', '营业执照')" title="点击查看证书">营业执照</span>
                    </div>
                    <div class="certificates-icons">
                        <img src="images/bottom1.jpg" alt="陕公网安备61010302001119号" class="cert-icon">
                        <img src="images/bottom2.jpg" alt="陕ICP备2023004908号-1" class="cert-icon">
                    </div>
                </div>
                <div class="footer-report">
                    <p class="report-intro">以下渠道均可投诉举报：互联网违法和不良信息，涉及未成年人保护、互联网算法推荐、谣言类信息等相关问题。</p>
                    <p class="report-contacts">
                        陕西省网络举报中心举报电话 029-63907150 / 029-63907152丨违法和不良信息举报电话12377丨全国文化和旅游市场举报电话12318
                    </p>
                    <div class="report-icons">
                        <a href="https://www.shaanxijubao.cn/" target="_blank" rel="noopener noreferrer" title="陕西省网络举报中心">
                            <img src="images/partners/p1.png" alt="陕西省网络举报中心" class="report-icon">
                        </a>
                        <a href="https://www.12377.cn/" target="_blank" rel="noopener noreferrer" title="中央网信办违法和不良信息举报中心">
                            <img src="images/partners/p2.png" alt="中央网信办违法和不良信息举报中心" class="report-icon">
                        </a>
                        <a href="https://jbts.mct.gov.cn/home" target="_blank" rel="noopener noreferrer" title="全国文化和旅游市场网上举报投诉处理系统">
                            <img src="images/partners/p3.png" alt="全国文化和旅游市场网上举报投诉处理系统" class="report-icon">
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 证书展示模态框 -->
    <div id="certificateModal" class="certificate-modal" onclick="closeCertificateModal()">
        <div class="certificate-modal-content" onclick="event.stopPropagation()">
            <span class="certificate-close-btn" onclick="closeCertificateModal()">&times;</span>
            <h3 id="certificateTitle" class="certificate-title"></h3>
            <div class="certificate-image-container">
                <img id="certificateImage" class="certificate-image" src="" alt="">
            </div>
        </div>
    </div>
    `;
}

// 滚动到联系我们部分的全局函数
function scrollToContact(event) {
    event.preventDefault();

    // 等待页脚加载完成后再滚动
    setTimeout(() => {
        const contactSection = document.querySelector('.footer-contact');
        if (contactSection) {
            // 平滑滚动到联系我们部分
            contactSection.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            // 更新URL hash
            window.history.pushState(null, null, '#footer-contact');

            // 设置导航高亮
            const navLinks = document.querySelectorAll('nav a');
            navLinks.forEach(link => link.classList.remove('active'));
            const contactLink = document.getElementById('nav-contact');
            if (contactLink) contactLink.classList.add('active');


        } else {
            console.log('联系我们部分未找到，可能页脚还未加载完成');
        }
    }, 500); // 等待500ms确保页脚已加载
}
