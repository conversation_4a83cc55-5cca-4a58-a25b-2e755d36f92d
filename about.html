<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关于我们 - 医药处方科技</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/about.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="shortcut icon" href="images/favicon.ico" type="image/x-icon">


</head>

<body>
    <!-- 导航栏 -->
    <header>
        <div class="container">
            <div class="logo">
                <img class="img" src="images/logo.png" alt="logo">
                <div style="position: relative;">
                    <h1>西安易复诊铭智网络科技有限公司</h1>
                    <span>Xi'an YiFuZhen MingZhi Network Technology Co., Ltd.</span>
                </div>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html" id="nav-home">首页</a></li>
                    <li><a href="about.html" id="nav-about" class="active">关于我们</a></li>
                    <li><a href="product.html" id="nav-product">产品服务</a></li>
                    <li><a href="team.html" id="nav-team">团队介绍</a></li>
                    <li><a href="#footer-contact" id="nav-contact" onclick="scrollToContact(event)">联系我们</a></li>
                </ul>
            </nav>
            <div class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <!-- 公司介绍 -->
    <section class="about-section">
        <div class="container">
            <h2 class="scroll-animation scroll-fade-up">公司简介</h2>
            <p class="scroll-animation scroll-fade-up textIndent">
                西安易复诊铭智网络科技有限公司，成立于2017年，总部位于西安，是一家集软件开发、技术服务、项目运营为一体的科技企业。公司自成立以来，始终以“惠民”、“惠政”为核心目标，长期深耕于“互联网+医药”领域，专注“处方流转平台”、“电子处方中心”、“处方流转应用及监管系统”的研发和应用，为政府处方监管需求和患者处方服务需求，提供完整的一体化服务体系。公司团队深耕行业多年，精准把握行业痛点，在全国各省市参与了多项“处方流转平台”和“医保双通道”项目建设，与政府和企业达成了长期、良好的合作关系，深得客户好评。
            </p>
            <p class="scroll-animation scroll-fade-up delay-1 textIndent">
                公司自有技术团队开发的《双通道服务监管系统》、《处方流转服务监管系统》、《医保电子处方监管系统》、《医保智慧控费服务系统》、《飞检智慧自查自纠系统》、《药店控费自纠自查辅助交互系统》、《电子处方云管理系统》、《处方流转一站式服务系统》、《基于电子处方中心的流转智慧管理服务系统》和《共享中药房智能管理系统》等已获得国家版权局计算机软件著作权登记证书认证。公司已取得互联网药品信息服务资格、增值电信业务（在线数据处理与交易业务处理业务）经营许可，并顺利通过了ISO9001质量管理体系和ISO27001信息安全管理体系认证。公司现有从事软硬件开发及相关支持人员130余人，其中软件设计、开发及SQA工程师63人，实施工程师22人，售前工程师6人，其他技术支持人员30余人，是国内资深的软件和信息服务类企业。
            </p>
            <p class="scroll-animation scroll-fade-up delay-2 textIndent">
                公司自创业以来，积极倡导“以人为本”，形成了独有的经营理念与企业文化。公司以“让每个人的用药精准触达”为愿景，发扬“专业、专注、奉献、创新”的精神，力争使自身成为国内先进的信息服务企业。公司对内建立了标准化、科学化和规范化的管理制度，对外建立了完善的用户培训和售后服务体系；积极引进先进的软件技术和科学管理经验，不断进行技术的创新和产品研究、开发与推广。公司在广大用户中树立了良好的信誉，依靠优秀的技术支持和高质的服务提高了公司的知名度，进一步扩大了公司的影响。
            </p>
            <p class="scroll-animation scroll-fade-up delay-3 textIndent"> 2024年11月5日，国家医疗保障局主办的“数字中国
                智慧医保”2024全国智慧医保大赛圆满落下帷幕。此次大赛中，我公司经初赛、复赛、决赛层层选拔，在来自全国31个省（区、市）的593支队伍中脱颖而出，参赛的《医保电子处方应用监管的实践案例》获得大赛三等奖，并分别与重庆市技术转移研究院、重庆市“专精特新基金”进行了成果转化的战略合作签约，充分体现了我公司在医保行业实践应用与技术创新的卓越实力。
            </p>
        </div>
    </section>

    <!-- 发展历程 -->
    <section class="about-section" style="background-color: #f9f9f9;">
        <div class="container">
            <h2 style="text-align: center;" class="scroll-animation scroll-fade-up">发展历程</h2>
            <div class="timeline scroll-animation scroll-zoom">
                <div class="timeline-item left">
                    <div class="timeline-content">
                        <h3>2010年</h3>
                        <p>公司成立，开始研发第一代处方管理系统</p>
                    </div>
                </div>
                <div class="timeline-item right">
                    <div class="timeline-content">
                        <h3>2012年</h3>
                        <p>推出首款智能处方审核系统，获得医药行业关注</p>
                    </div>
                </div>
                <div class="timeline-item left">
                    <div class="timeline-content">
                        <h3>2014年</h3>
                        <p>获得A轮融资，产品覆盖全国百家医院</p>
                    </div>
                </div>
                <div class="timeline-item right">
                    <div class="timeline-content">
                        <h3>2016年</h3>
                        <p>推出基于云计算的处方大数据分析平台</p>
                    </div>
                </div>
                <div class="timeline-item left">
                    <div class="timeline-content">
                        <h3>2018年</h3>
                        <p>获得国家高新技术企业认证，产品覆盖千家医疗机构</p>
                    </div>
                </div>
                <div class="timeline-item right">
                    <div class="timeline-content">
                        <h3>2020年</h3>
                        <p>推出AI驱动的新一代处方智能管理系统</p>
                    </div>
                </div>
                <div class="timeline-item left">
                    <div class="timeline-content">
                        <h3>2023年</h3>
                        <p>完成B轮融资，开始国际市场拓展</p>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- 荣誉资质 -->
    <section class="about-section">
        <div class="container">
            <h2 class="scroll-animation scroll-fade-up">荣誉资质</h2>
            <div style="display: flex; flex-wrap: wrap; gap: 30px; margin-top: 40px;">
                <div class="scroll-animation scroll-fade-up hover-float certificate-card"
                    style="flex: 1; min-width: 230px; text-align: center; padding: 30px; background-color: #fff; box-shadow: var(--box-shadow); border-radius: 10px;">
                    <img class="certificate-img" style="width: 100%; height: 100%; cursor: pointer; transition: transform 0.3s ease;" src="images/certificate/zzry1.png" alt="荣誉资质证书1" onclick="openImageModal(this)">
                </div>
                <div class="scroll-animation scroll-fade-up delay-1 hover-float certificate-card"
                    style="flex: 1; min-width: 230px; text-align: center; padding: 30px; background-color: #fff; box-shadow: var(--box-shadow); border-radius: 10px;">
                    <img class="certificate-img" style="width: 100%; height: 100%; cursor: pointer; transition: transform 0.3s ease;" src="images/certificate/zzry2.png" alt="荣誉资质证书2" onclick="openImageModal(this)">
                </div>
                <div class="scroll-animation scroll-fade-up delay-2 hover-float certificate-card"
                    style="flex: 1; min-width: 230px; text-align: center; padding: 30px; background-color: #fff; box-shadow: var(--box-shadow); border-radius: 10px;">
                    <img class="certificate-img" style="width: 100%; height: 100%; cursor: pointer; transition: transform 0.3s ease;" src="images/certificate/zzry3.png" alt="荣誉资质证书3" onclick="openImageModal(this)">
                </div>
                <div class="scroll-animation scroll-fade-up delay-3 hover-float certificate-card"
                    style="flex: 1; min-width: 230px; text-align: center; padding: 30px; background-color: #fff; box-shadow: var(--box-shadow); border-radius: 10px;">
                    <img class="certificate-img" style="width: 100%; height: 100%; cursor: pointer; transition: transform 0.3s ease;" src="images/certificate/zzry4.png" alt="荣誉资质证书4" onclick="openImageModal(this)">
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>西安易复诊铭智网络科技有限公司</h2>
                    <p>专注"互联网+医药"领域的科技创新企业</p>
                </div>
                <div class="footer-links">
                    <h3>快速链接</h3>
                    <ul>
                        <li><a href="index.html">首页</a></li>
                        <li><a href="about.html">关于我们</a></li>
                        <li><a href="product.html">产品服务</a></li>
                        <li><a href="team.html">团队介绍</a></li>
                        <li><a href="#footer-contact" onclick="scrollToContact(event)">联系我们</a></li>
                    </ul>
                </div>
                <div class="footer-contact" id="footer-contact">
                    <h3>联系方式</h3>
                    <div class="contact-items-row">
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>西安市未央区太华北路与凤城三路交口西南-四海中心B座612</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <span>18192283054</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-clock"></i>
                            <span>9:00-18:00</span>
                        </div>
                    </div>
                </div>
                <div class="footer-follow">
                    <h3>关注我们</h3>
                    <p>扫码关注企业公众号，获取最新资讯</p>
                    <div class="qr-code-container">
                        <img src="images/gzh.jpg" alt="企业公众号二维码">
                        <p>微信公众号</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-certificates">
                    <div class="certificates-line">
                        <a href="#" class="cert-text-link" onclick="showCertificate('images/certificate/zzry5.png', '互联网药品信息服务资格证'); return false;" title="点击查看证书">
                            互联网药品信息服务资格证编号：(陕)-经营性-2023-0024
                        </a>
                        <a href="#" class="cert-text-link" onclick="showCertificate('images/certificate/zzry2.png', '增值电信业务经营许可证'); return false;" title="点击查看证书">
                            增值电信业务经营许可证编号：陕B2-20230168
                        </a>
                        <a href="#" class="cert-text-link" onclick="showCertificate('images/certificate/zzry6.jpg', '营业执照'); return false;" title="点击查看证书">
                            营业执照
                        </a>
                    </div>
                    <div class="certificates-links">
                        <a href="https://beian.mps.gov.cn/#/query/webSearch" target="_blank" rel="noopener noreferrer" class="cert-text-link" title="点击查询公安备案信息">
                            陕公网安备61010302001119号
                        </a>
                        <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank" rel="noopener noreferrer" class="cert-text-link" title="点击查询ICP备案信息">
                            陕ICP备2023004908号-1
                        </a>
                    </div>
                </div>
                <div class="footer-report">
                    <p class="report-intro">以下渠道均可投诉举报：互联网违法和不良信息，涉及未成年人保护、互联网算法推荐、谣言类信息等相关问题。</p>
                    <p class="report-contacts">
                        陕西省网络举报中心举报电话 029-63907150 / 029-63907152丨违法和不良信息举报电话12377丨全国文化和旅游市场举报电话12318
                    </p>
                    <div class="report-icons">
                        <a href="https://www.shaanxijubao.cn/" target="_blank" rel="noopener noreferrer" title="陕西省网络举报中心">
                            <img src="images/bottom/bottom1.jpg" alt="陕西省网络举报中心" class="report-icon">
                        </a>
                        <a href="https://www.12377.cn/" target="_blank" rel="noopener noreferrer" title="中央网信办违法和不良信息举报中心">
                            <img src="images/bottom/bottom2.jpg" alt="中央网信办违法和不良信息举报中心" class="report-icon">
                        </a>
                        <a href="https://jbts.mct.gov.cn/home" target="_blank" rel="noopener noreferrer" title="全国文化和旅游市场网上举报投诉处理系统">
                            <img src="images/bottom/bottom3.jpg" alt="全国文化和旅游市场网上举报投诉处理系统" class="report-icon">
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 证书展示模态框 -->
    <div id="certificateModal" class="certificate-modal" onclick="closeCertificateModal()">
        <div class="certificate-modal-content" onclick="event.stopPropagation()">
            <span class="certificate-close-btn" onclick="closeCertificateModal()">&times;</span>
            <h3 id="certificateTitle" class="certificate-title"></h3>
            <div class="certificate-image-container">
                <img id="certificateImage" class="certificate-image" src="" alt="">
            </div>
        </div>
    </div>



    <!-- 图片放大模态框 -->
    <div id="imageModal" class="image-modal" onclick="closeImageModal()">
        <div class="modal-content">
            <span class="close-btn" onclick="closeImageModal()">&times;</span>
            <img id="modalImage" class="modal-image" src="" alt="">
            <div class="image-nav">
                <button class="nav-btn prev-btn" onclick="prevImage(event)">&lt;</button>
                <button class="nav-btn next-btn" onclick="nextImage(event)">&gt;</button>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/common.js"></script>
    <script src="js/about.js"></script>
</body>

</html>