/* Team页面专用样式 */

.team-hero {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    padding: 120px 0 80px;
    text-align: center;
}

.team-hero h1 {
    font-size: 3rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.team-hero p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.team-section {
    padding: 120px 0;
}

.team-members {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-top: 60px;
}

.member {
    background: white;
    border-radius: 20px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-height: 520px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.member::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
}

.member:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.member-img {
    width: 180px;
    height: 240px;
    border-radius: 15px;
    margin: 0 auto 20px;
    overflow: hidden;
    border: 4px solid #f8f9fa;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
}

.member-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center top;
    transition: transform 0.3s ease;
}

.member:hover .member-img img {
    transform: scale(1.05);
}

/* 为全身照添加渐变遮罩效果 */
.member-img::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50px;
    background: linear-gradient(transparent, rgba(255, 255, 255, 0.1));
    pointer-events: none;
}

.member-info h3 {
    font-size: 1.3rem;
    color: var(--dark-color);
    margin-bottom: 8px;
    font-weight: 600;
}

.member-info p {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 15px;
    font-size: 1rem;
}

.member-description {
    color: #666;
    line-height: 1.5;
    margin-bottom: 20px;
    font-size: 0.85rem;
    flex-grow: 1;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.social-links a {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: #f8f9fa;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    text-decoration: none;
    font-size: 0.9rem;
}

.social-links a:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* 团队统计 */
.team-stats {
    background: #f8f9fa;
    padding: 60px 0;
    margin: 60px 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    text-align: center;
}

.stat-item {
    padding: 20px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1.1rem;
    color: #666;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .team-members {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }

    .member {
        min-height: 450px;
    }

    .member-img {
        width: 150px;
        height: 200px;
    }
}

@media (max-width: 768px) {
    .team-hero {
        padding: 100px 0 60px;
    }

    .team-hero h1 {
        font-size: 2rem;
    }

    .team-hero p {
        font-size: 1rem;
        padding: 0 20px;
    }

    .team-section {
        padding: 60px 0;
    }

    .team-members {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        margin-top: 40px;
    }

    .member {
        padding: 25px 15px;
        min-height: 400px;
    }

    .member-img {
        width: 120px;
        height: 160px;
        margin-bottom: 15px;
    }

    .member-info h3 {
        font-size: 1.1rem;
    }

    .member-info p {
        font-size: 0.9rem;
    }

    .member-description {
        font-size: 0.8rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .team-members {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .member {
        min-height: auto;
        padding: 20px 15px;
    }

    .member-img {
        width: 100px;
        height: 140px;
    }
}
