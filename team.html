<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>团队介绍 - 西安易复诊铭智网络科技有限公司</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 团队页面特定样式 */
        .team-hero {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            padding: 120px 0 80px;
            text-align: center;
        }
        
        .team-hero h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .team-hero p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .team-section {
            padding: 120px 0;
        }
        
        .team-members {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            margin-top: 60px;
        }
        
        .member {
            background: white;
            border-radius: 20px;
            padding: 30px 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            min-height: 520px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .member::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
        }
        
        .member:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .member-img {
            width: 180px;
            height: 240px;
            border-radius: 15px;
            margin: 0 auto 20px;
            overflow: hidden;
            border: 4px solid #f8f9fa;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .member-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center top;
            transition: transform 0.3s ease;
        }

        .member:hover .member-img img {
            transform: scale(1.05);
        }

        /* 为全身照添加渐变遮罩效果 */
        .member-img::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: linear-gradient(transparent, rgba(255, 255, 255, 0.1));
            pointer-events: none;
        }
        
        .member-info h3 {
            font-size: 1.3rem;
            color: var(--dark-color);
            margin-bottom: 8px;
            font-weight: 600;
        }

        .member-info p {
            color: var(--primary-color);
            font-weight: 500;
            margin-bottom: 15px;
            font-size: 1rem;
        }

        .member-description {
            color: #666;
            line-height: 1.5;
            margin-bottom: 20px;
            font-size: 0.85rem;
            flex-grow: 1;
        }
        
        .social-links {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        
        .social-links a {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: #f8f9fa;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        .social-links a:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }
        
        /* 团队统计 */
        .team-stats {
            background: #f8f9fa;
            padding: 60px 0;
            margin: 60px 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            text-align: center;
        }
        
        .stat-item {
            padding: 20px;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1rem;
            color: #666;
            font-weight: 500;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .team-members {
                grid-template-columns: repeat(2, 1fr);
                gap: 25px;
            }

            .member {
                min-height: 450px;
            }

            .member-img {
                width: 150px;
                height: 200px;
            }
        }

        @media (max-width: 768px) {
            .team-hero {
                padding: 100px 0 60px;
            }

            .team-hero h1 {
                font-size: 2rem;
            }

            .team-hero p {
                font-size: 1rem;
                padding: 0 20px;
            }

            .team-section {
                padding: 60px 0;
            }

            .team-members {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
                margin-top: 40px;
            }

            .member {
                padding: 25px 15px;
                min-height: 400px;
            }

            .member-img {
                width: 120px;
                height: 160px;
                margin-bottom: 15px;
            }

            .member-info h3 {
                font-size: 1.1rem;
            }

            .member-info p {
                font-size: 0.9rem;
            }

            .member-description {
                font-size: 0.8rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }

            .stat-number {
                font-size: 2rem;
            }
        }

        @media (max-width: 480px) {
            .team-members {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .member {
                min-height: auto;
                padding: 20px 15px;
            }

            .member-img {
                width: 100px;
                height: 140px;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <header>
        <div class="container">
            <div class="logo">
                <img class="img" src="images/logo.png" alt="logo">
                <div style="position: relative;">
                    <h1>西安易复诊铭智网络科技有限公司</h1>
                    <span>Xi'an YiFuZhen MingZhi Network Technology Co., Ltd.</span>
                </div>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html" id="nav-home">首页</a></li>
                    <li><a href="about.html" id="nav-about">关于我们</a></li>
                    <li><a href="product.html" id="nav-product">产品服务</a></li>
                    <li><a href="team.html" id="nav-team" class="active">团队介绍</a></li>
                    <li><a href="#footer-contact" id="nav-contact" onclick="scrollToContact(event)">联系我们</a></li>
                </ul>
            </nav>
            <div class="mobile-menu-btn">
                <i class="fas fa-bars"></i>
            </div>
        </div>
    </header>

    <!-- 团队成员 -->
    <section class="team-section">
        <div class="container">
            <div class="section-header scroll-animation scroll-fade-up">
                <h2>核心团队</h2>
                <p>汇聚行业精英，打造专业服务团队</p>
            </div>
            <div class="team-members">
                <div class="member scroll-animation scroll-fade-up">
                    <div class="member-img">
                        <img src="images/team-1.jpg" alt="张医生">
                    </div>
                    <div class="member-info">
                        <h3>张医生</h3>
                        <p>首席医疗顾问</p>
                        <div class="member-description">
                            拥有20年临床经验的资深医师，专注于药物治疗和处方优化，曾在多家三甲医院担任主任医师，在合理用药领域有深入研究。
                        </div>
                        <div class="social-links">
                            <a href="#" title="LinkedIn"><i class="fab fa-linkedin"></i></a>
                            <a href="#" title="微信"><i class="fab fa-weixin"></i></a>
                        </div>
                    </div>
                </div>
                <div class="member scroll-animation scroll-fade-up delay-1">
                    <div class="member-img">
                        <img src="images/team-2.jpg" alt="李工程师">
                    </div>
                    <div class="member-info">
                        <h3>李工程师</h3>
                        <p>技术总监</p>
                        <div class="member-description">
                            计算机科学硕士，15年软件开发经验，专精于医疗信息化系统架构设计，曾主导多个大型医院信息系统的建设。
                        </div>
                        <div class="social-links">
                            <a href="#" title="LinkedIn"><i class="fab fa-linkedin"></i></a>
                            <a href="#" title="GitHub"><i class="fab fa-github"></i></a>
                        </div>
                    </div>
                </div>
                <div class="member scroll-animation scroll-fade-up delay-2">
                    <div class="member-img">
                        <img src="images/team-3.jpg" alt="王博士">
                    </div>
                    <div class="member-info">
                        <h3>王博士</h3>
                        <p>数据科学家</p>
                        <div class="member-description">
                            生物信息学博士，专注于医药大数据分析和人工智能算法研究，在药物相互作用预测和个性化用药方面有突出贡献。
                        </div>
                        <div class="social-links">
                            <a href="#" title="LinkedIn"><i class="fab fa-linkedin"></i></a>
                            <a href="#" title="ResearchGate"><i class="fab fa-researchgate"></i></a>
                        </div>
                    </div>
                </div>
                <div class="member scroll-animation scroll-fade-up delay-3">
                    <div class="member-img">
                        <img src="images/team-4.jpg" alt="赵经理">
                    </div>
                    <div class="member-info">
                        <h3>赵经理</h3>
                        <p>市场总监</p>
                        <div class="member-description">
                            MBA工商管理硕士，10年医药行业市场营销经验，深谙医疗机构需求，擅长制定精准的市场策略和客户服务方案。
                        </div>
                        <div class="social-links">
                            <a href="#" title="LinkedIn"><i class="fab fa-linkedin"></i></a>
                            <a href="#" title="微信"><i class="fab fa-weixin"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 团队统计 -->
    <section class="team-stats">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item scroll-animation scroll-fade-up">
                    <div class="stat-number">20+</div>
                    <div class="stat-label">团队成员</div>
                </div>
                <div class="stat-item scroll-animation scroll-fade-up delay-1">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">平均从业年限</div>
                </div>
                <div class="stat-item scroll-animation scroll-fade-up delay-2">
                    <div class="stat-number">100+</div>
                    <div class="stat-label">服务客户数</div>
                </div>
                <div class="stat-item scroll-animation scroll-fade-up delay-3">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">技术支持</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>西安易复诊铭智网络科技有限公司</h2>
                    <p>专注"互联网+医药"领域的科技创新企业</p>
                </div>
                <div class="footer-links">
                    <h3>快速链接</h3>
                    <ul>
                        <li><a href="index.html">首页</a></li>
                        <li><a href="about.html">关于我们</a></li>
                        <li><a href="product.html">产品服务</a></li>
                        <li><a href="team.html">团队介绍</a></li>
                        <li><a href="#footer-contact" onclick="scrollToContact(event)">联系我们</a></li>
                    </ul>
                </div>
                <div class="footer-contact" id="footer-contact">
                    <h3>联系方式</h3>
                    <div class="contact-items-row">
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>西安市未央区太华北路与凤城三路交口西南-四海中心B座612</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <span>18192283054</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-clock"></i>
                            <span>9:00-18:00</span>
                        </div>
                    </div>
                </div>
                <div class="footer-follow">
                    <h3>关注我们</h3>
                    <p>扫码关注企业公众号，获取最新资讯</p>
                    <div class="qr-code-container">
                        <img src="images/gzh.jpg" alt="企业公众号二维码">
                        <p>微信公众号</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="footer-certificates">
                    <div class="certificates-line">
                        <a href="#" class="cert-text-link" onclick="showCertificate('images/certificate/zzry5.png', '互联网药品信息服务资格证'); return false;" title="点击查看证书">
                            互联网药品信息服务资格证编号：(陕)-经营性-2023-0024
                        </a>
                        <a href="#" class="cert-text-link" onclick="showCertificate('images/certificate/zzry2.png', '增值电信业务经营许可证'); return false;" title="点击查看证书">
                            增值电信业务经营许可证编号：陕B2-20230168
                        </a>
                        <a href="#" class="cert-text-link" onclick="showCertificate('images/certificate/zzry6.jpg', '营业执照'); return false;" title="点击查看证书">
                            营业执照
                        </a>
                    </div>
                    <div class="certificates-links">
                        <a href="https://beian.mps.gov.cn/#/query/webSearch" target="_blank" rel="noopener noreferrer" class="cert-text-link" title="点击查询公安备案信息">
                            陕公网安备61010302001119号
                        </a>
                        <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank" rel="noopener noreferrer" class="cert-text-link" title="点击查询ICP备案信息">
                            陕ICP备2023004908号-1
                        </a>
                    </div>
                </div>
                <div class="footer-report">
                    <p class="report-intro">以下渠道均可投诉举报：互联网违法和不良信息，涉及未成年人保护、互联网算法推荐、谣言类信息等相关问题。</p>
                    <p class="report-contacts">
                        陕西省网络举报中心举报电话 029-63907150 / 029-63907152丨违法和不良信息举报电话12377丨全国文化和旅游市场举报电话12318
                    </p>
                    <div class="report-icons">
                        <a href="https://www.shaanxijubao.cn/" target="_blank" rel="noopener noreferrer" title="陕西省网络举报中心">
                            <img src="images/partners/p1.png" alt="陕西省网络举报中心" class="report-icon">
                        </a>
                        <a href="https://www.12377.cn/" target="_blank" rel="noopener noreferrer" title="中央网信办违法和不良信息举报中心">
                            <img src="images/partners/p2.png" alt="中央网信办违法和不良信息举报中心" class="report-icon">
                        </a>
                        <a href="https://jbts.mct.gov.cn/home" target="_blank" rel="noopener noreferrer" title="全国文化和旅游市场网上举报投诉处理系统">
                            <img src="images/partners/p3.png" alt="全国文化和旅游市场网上举报投诉处理系统" class="report-icon">
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- 证书展示模态框 -->
    <div id="certificateModal" class="certificate-modal" onclick="closeCertificateModal()">
        <div class="certificate-modal-content" onclick="event.stopPropagation()">
            <span class="certificate-close-btn" onclick="closeCertificateModal()">&times;</span>
            <h3 id="certificateTitle" class="certificate-title"></h3>
            <div class="certificate-image-container">
                <img id="certificateImage" class="certificate-image" src="" alt="">
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
    <script>
        // 证书展示功能
        function showCertificate(imageSrc, title) {
            const modal = document.getElementById('certificateModal');
            const modalImg = document.getElementById('certificateImage');
            const modalTitle = document.getElementById('certificateTitle');

            modal.style.display = 'flex';
            modalImg.src = imageSrc;
            modalImg.alt = title;
            modalTitle.textContent = title;
            document.body.style.overflow = 'hidden'; // 防止背景滚动
        }

        function closeCertificateModal() {
            const modal = document.getElementById('certificateModal');
            modal.style.display = 'none';
            document.body.style.overflow = 'auto'; // 恢复滚动
        }

        // 滚动到联系我们部分的函数
        function scrollToContact(event) {
            event.preventDefault();

            const contactSection = document.querySelector('.footer-contact');
            if (contactSection) {
                // 平滑滚动到联系我们部分
                contactSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // 更新URL hash
                window.history.pushState(null, null, '#footer-contact');

                // 设置导航高亮
                const navLinks = document.querySelectorAll('nav a');
                navLinks.forEach(link => link.classList.remove('active'));
                const contactLink = document.getElementById('nav-contact');
                if (contactLink) contactLink.classList.add('active');
            }
        }

        // 键盘ESC关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeCertificateModal();
            }
        });
    </script>
</body>
</html>
