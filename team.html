<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>团队介绍 - 西安易复诊铭智网络科技有限公司</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 团队页面特定样式 */
        .team-hero {
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
            color: white;
            padding: 120px 0 80px;
            text-align: center;
        }
        
        .team-hero h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .team-hero p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .team-section {
            padding: 120px 0;
        }
        
        .team-members {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            margin-top: 60px;
        }
        
        .member {
            background: white;
            border-radius: 20px;
            padding: 30px 20px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            min-height: 520px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .member::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--primary-color), #0056b3);
        }
        
        .member:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .member-img {
            width: 180px;
            height: 240px;
            border-radius: 15px;
            margin: 0 auto 20px;
            overflow: hidden;
            border: 4px solid #f8f9fa;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            position: relative;
        }
        
        .member-img img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center top;
            transition: transform 0.3s ease;
        }

        .member:hover .member-img img {
            transform: scale(1.05);
        }

        /* 为全身照添加渐变遮罩效果 */
        .member-img::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 50px;
            background: linear-gradient(transparent, rgba(255, 255, 255, 0.1));
            pointer-events: none;
        }
        
        .member-info h3 {
            font-size: 1.3rem;
            color: var(--dark-color);
            margin-bottom: 8px;
            font-weight: 600;
        }

        .member-info p {
            color: var(--primary-color);
            font-weight: 500;
            margin-bottom: 15px;
            font-size: 1rem;
        }

        .member-description {
            color: #666;
            line-height: 1.5;
            margin-bottom: 20px;
            font-size: 0.85rem;
            flex-grow: 1;
        }
        
        .social-links {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        
        .social-links a {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: #f8f9fa;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 0.9rem;
        }
        
        .social-links a:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }
        
        /* 团队统计 */
        .team-stats {
            background: #f8f9fa;
            padding: 60px 0;
            margin: 60px 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            text-align: center;
        }
        
        .stat-item {
            padding: 20px;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1rem;
            color: #666;
            font-weight: 500;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .team-members {
                grid-template-columns: repeat(2, 1fr);
                gap: 25px;
            }

            .member {
                min-height: 450px;
            }

            .member-img {
                width: 150px;
                height: 200px;
            }
        }

        @media (max-width: 768px) {
            .team-hero {
                padding: 100px 0 60px;
            }

            .team-hero h1 {
                font-size: 2rem;
            }

            .team-hero p {
                font-size: 1rem;
                padding: 0 20px;
            }

            .team-section {
                padding: 60px 0;
            }

            .team-members {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
                margin-top: 40px;
            }

            .member {
                padding: 25px 15px;
                min-height: 400px;
            }

            .member-img {
                width: 120px;
                height: 160px;
                margin-bottom: 15px;
            }

            .member-info h3 {
                font-size: 1.1rem;
            }

            .member-info p {
                font-size: 0.9rem;
            }

            .member-description {
                font-size: 0.8rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }

            .stat-number {
                font-size: 2rem;
            }
        }

        @media (max-width: 480px) {
            .team-members {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .member {
                min-height: auto;
                padding: 20px 15px;
            }

            .member-img {
                width: 100px;
                height: 140px;
            }
        }
    </style>
</head>
<body>
    <!-- 页眉占位符 -->
    <div id="header-placeholder"></div>

    <!-- 团队成员 -->
    <section class="team-section">
        <div class="container">
            <div class="section-header scroll-animation scroll-fade-up">
                <h2>核心团队</h2>
                <p>汇聚行业精英，打造专业服务团队</p>
            </div>
            <div class="team-members">
                <div class="member scroll-animation scroll-fade-up">
                    <div class="member-img">
                        <img src="images/team-1.jpg" alt="张医生">
                    </div>
                    <div class="member-info">
                        <h3>张医生</h3>
                        <p>首席医疗顾问</p>
                        <div class="member-description">
                            拥有20年临床经验的资深医师，专注于药物治疗和处方优化，曾在多家三甲医院担任主任医师，在合理用药领域有深入研究。
                        </div>
                        <div class="social-links">
                            <a href="#" title="LinkedIn"><i class="fab fa-linkedin"></i></a>
                            <a href="#" title="微信"><i class="fab fa-weixin"></i></a>
                        </div>
                    </div>
                </div>
                <div class="member scroll-animation scroll-fade-up delay-1">
                    <div class="member-img">
                        <img src="images/team-2.jpg" alt="李工程师">
                    </div>
                    <div class="member-info">
                        <h3>李工程师</h3>
                        <p>技术总监</p>
                        <div class="member-description">
                            计算机科学硕士，15年软件开发经验，专精于医疗信息化系统架构设计，曾主导多个大型医院信息系统的建设。
                        </div>
                        <div class="social-links">
                            <a href="#" title="LinkedIn"><i class="fab fa-linkedin"></i></a>
                            <a href="#" title="GitHub"><i class="fab fa-github"></i></a>
                        </div>
                    </div>
                </div>
                <div class="member scroll-animation scroll-fade-up delay-2">
                    <div class="member-img">
                        <img src="images/team-3.jpg" alt="王博士">
                    </div>
                    <div class="member-info">
                        <h3>王博士</h3>
                        <p>数据科学家</p>
                        <div class="member-description">
                            生物信息学博士，专注于医药大数据分析和人工智能算法研究，在药物相互作用预测和个性化用药方面有突出贡献。
                        </div>
                        <div class="social-links">
                            <a href="#" title="LinkedIn"><i class="fab fa-linkedin"></i></a>
                            <a href="#" title="ResearchGate"><i class="fab fa-researchgate"></i></a>
                        </div>
                    </div>
                </div>
                <div class="member scroll-animation scroll-fade-up delay-3">
                    <div class="member-img">
                        <img src="images/team-4.jpg" alt="赵经理">
                    </div>
                    <div class="member-info">
                        <h3>赵经理</h3>
                        <p>市场总监</p>
                        <div class="member-description">
                            MBA工商管理硕士，10年医药行业市场营销经验，深谙医疗机构需求，擅长制定精准的市场策略和客户服务方案。
                        </div>
                        <div class="social-links">
                            <a href="#" title="LinkedIn"><i class="fab fa-linkedin"></i></a>
                            <a href="#" title="微信"><i class="fab fa-weixin"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 团队统计 -->
    <section class="team-stats">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item scroll-animation scroll-fade-up">
                    <div class="stat-number">20+</div>
                    <div class="stat-label">团队成员</div>
                </div>
                <div class="stat-item scroll-animation scroll-fade-up delay-1">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">平均从业年限</div>
                </div>
                <div class="stat-item scroll-animation scroll-fade-up delay-2">
                    <div class="stat-number">100+</div>
                    <div class="stat-label">服务客户数</div>
                </div>
                <div class="stat-item scroll-animation scroll-fade-up delay-3">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">技术支持</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚占位符 -->
    <div id="footer-placeholder"></div>

    <script src="js/includes.js"></script>
    <script src="js/main.js"></script>
    <script src="js/animations.js"></script>
</body>
</html>
